type Menu = {
  /** 表示名 */
  label: string;
  /** 遷移先パス */
  pathname: string;
  /** 遷移先URLクエリ */
  query?: string | undefined;
  /** 同一メニューとみなすパス（アクティブ表示になる） */
  relatedPaths: string[];
};

/** 共有Headerメニュー */
export const headerMenuData: Menu[] = [
  { label: "ホーム", pathname: "/start", relatedPaths: [] },
  { label: "受付", pathname: "/reception", relatedPaths: [] },
  { label: "予約", pathname: "/calendar", relatedPaths: [] },
];

/** 服薬指導システムの共有Headerメニュー */
export const pharmacyHeaderMenuData: Menu[] = [
  {
    label: "処方箋モバイルオーダー",
    pathname: "/pharmacy/prescription",
    relatedPaths: [],
  },
  {
    label: "オンライン服薬指導",
    pathname: "/pharmacy/reception",
    relatedPaths: [],
  },
];

/** 設定画面メニュー */
export const settingMenuData: Menu[] = [
  {
    label: "医療機関情報",
    pathname: "/setting/hospital",
    relatedPaths: [],
  },
  {
    label: "決済サービス利用情報",
    pathname: "/setting/payment",
    relatedPaths: [],
  },
  {
    label: "アカウント管理",
    pathname: "/setting/account",
    relatedPaths: [],
  },
  {
    label: "診療科・診療メニュー設定",
    pathname: "/setting/dep",
    relatedPaths: [],
  },
  {
    label: "予約カレンダー管理",
    pathname: "/setting/calendar",
    relatedPaths: [],
  },
  {
    label: "Web問診票管理",
    pathname: "/setting/survey",
    relatedPaths: [],
  },
  {
    label: "オンライン資格確認",
    pathname: "/setting/online-qualification",
    relatedPaths: [],
  },
  {
    label: "監査ログ",
    pathname: "/setting/audit",
    relatedPaths: [],
  },
  {
    label: "データ提出ファイル作成",
    pathname: "/setting/create-submission-data",
    relatedPaths: [],
  },
  {
    label: "電子証明書管理",
    pathname: "/setting/cert",
    relatedPaths: [],
  },
  {
    label: "文書雛形管理",
    pathname: "/setting/document-template",
    relatedPaths: [],
  },
  {
    label: "電子処方箋設定",
    pathname: "/setting/elec-prescription",
    relatedPaths: [],
  },
  {
    label: "マスタ管理設定",
    pathname: "/setting/master",
    relatedPaths: [],
  },
  {
    label: "算定設定",
    pathname: "/setting/calculate/support",
    relatedPaths: ["/setting/calculate/automatic"],
  },
  {
    label: "カルテPDF出力",
    pathname: "/setting/karte-pdf",
    relatedPaths: [],
  },
  {
    label: "ラベル設定",
    pathname: "/setting/label",
    relatedPaths: [],
  },
  {
    label: "検査会社管理",
    pathname: "/setting/testing-company",
    relatedPaths: [],
  },
  { label: "セット設定", pathname: "/setting/set", relatedPaths: [] },
  // {
  //   label: "受付ステータス設定",
  //   pathname: "/setting/reception-status",
  //   relatedPaths: [],
  // },
  {
    label: "院内検査項目",
    pathname: "/setting/internal-exam",
    relatedPaths: [],
  },
  {
    label: "レセプト",
    pathname: "/setting/receipt",
    relatedPaths: [],
  },
  {
    label: "規約・ポリシー等",
    pathname: "/setting/terms",
    relatedPaths: [],
  },
  {
    label: "帳票設定",
    pathname: "/setting/ledger",
    relatedPaths: [],
  },
  {
    label: "加算時間設定",
    pathname: "/setting/addition-time",
    relatedPaths: [],
  },
  {
    label: "休日設定",
    pathname: "/setting/holiday",
    relatedPaths: [],
  },
  {
    label: "投与チェック設定",
    pathname: "/setting/drug-check",
    relatedPaths: [],
  },
  {
    label: "FastCheckOut連携",
    pathname: "/setting/fco-connection",
    relatedPaths: [],
  },
  {
    label: "その他設定",
    pathname: "/setting/miscellaneous",
    relatedPaths: [],
  },
  {
    label: "ホームページ掲載用バナー",
    pathname: "https://support-aichart.gmo-healthtech.com/hc/ja/p/images",
    relatedPaths: [],
  },
];

/** 服薬指導システムの設定画面メニュー */
export const pharmacySettingMenuData: Menu[] = [
  {
    label: "アカウント管理",
    pathname: "/pharmacy/setting/account",
    relatedPaths: [],
  },
  {
    label: "休日カレンダー管理",
    pathname: "/pharmacy/setting/calendar",
    relatedPaths: [],
  },
  {
    label: "監査ログ",
    pathname: "/pharmacy/setting/audit",
    relatedPaths: [],
  },
  {
    label: "電子証明書管理",
    pathname: "/pharmacy/setting/cert",
    relatedPaths: [],
  },
];
