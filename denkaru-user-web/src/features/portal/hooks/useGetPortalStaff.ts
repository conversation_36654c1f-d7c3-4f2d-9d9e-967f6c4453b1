import { useGetPortalHospitalStaffsQuery } from "@/apis/gql/operations/__generated__/portal-staff";
import { useErrorHandler } from "@/hooks/useErrorHandler";

export const useGetPortalStaff = () => {
  const { handleError } = useErrorHandler();

  const { loading, data: portalStaffData } = useGetPortalHospitalStaffsQuery({
    onError: (error) => {
      handleError({ error, commonMessage: "医師情報の取得に失敗しました" });
    },
  });

  return {
    loading,
    staffs: portalStaffData?.getPortalHospitalStaffs,
  };
};
