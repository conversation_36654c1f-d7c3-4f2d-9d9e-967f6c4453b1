import React from "react";

import Link from "next/link";
import styled from "styled-components";
import { Flex } from "antd";

import { usePortal } from "@/providers/PortalProvider";
// eslint-disable-next-line import/no-restricted-paths
import { PortalPublicationSegment } from "@/features/setting-portal/ui/PortalPublicationSegment";

const LeftContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: calc(100vh - 68px);
`;

const LeftContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 380px;
  background-color: #005bac;
  border-top-right-radius: 12px;
  padding: 20px;
  gap: 40px;
`;

const Title = styled.div`
  padding-left: 20px;
  color: #243544;
  font-size: 20px;
  font-weight: bold;
  line-height: 1;
  cursor: default;

  & > span:first-child {
    font-family: Roboto;
  }
`;

const SectionTitle = styled.span`
  color: white;
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
  cursor: default;
`;

const SectionCard = styled.div`
  background: white;
  border-radius: 8px;
  margin-top: 8px;
`;

const MenuItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e2e3e5;
  padding: 20px 12px;

  &:last-child {
    border-bottom: none;
  }
`;

const MenuContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 5px;
  flex: 1;
`;

const MenuTitle = styled.span`
  font-size: 16px;
  color: #243544;
  line-height: 1;
  cursor: default;
`;

const MenuDescription = styled.span`
  font-size: 14px;
  color: #6a757d;
  line-height: 1;
  cursor: default;
`;

const ActionButton = styled(Link)`
  color: #007aff;
  font-size: 14px;
  cursor: pointer;
`;

const ActionWrapper = styled.div`
  display: flex;
  margin-left: auto;
  gap: 20px;
`;

export const LeftContent: React.FC = () => {
  const { hospital, hospitalInfo } = usePortal();

  return (
    <LeftContentWrapper>
      <Title>
        <span>GMO</span>
        <span>クリニック・マップ管理画面</span>
      </Title>
      <LeftContentContainer>
        <div>
          <Flex justify="space-between" align="center">
            <SectionTitle>掲載情報</SectionTitle>
            <ActionWrapper>
              {(() => {
                if (
                  hospital === null ||
                  typeof hospital === "undefined" ||
                  typeof hospitalInfo?.status === "undefined"
                ) {
                  return null;
                }
                return (
                  <PortalPublicationSegment
                    hospital={hospital}
                    hpStatus={hospitalInfo?.status}
                  />
                );
              })()}
            </ActionWrapper>
          </Flex>
          <SectionCard>
            <MenuItem>
              <MenuContent>
                <MenuTitle>お知らせ掲載</MenuTitle>
                <MenuDescription>説明文</MenuDescription>
              </MenuContent>
              <ActionButton href="/setting/portal/news">編集</ActionButton>
            </MenuItem>
            <MenuItem>
              <MenuContent>
                <MenuTitle>基本情報</MenuTitle>
                <MenuDescription>説明文</MenuDescription>
              </MenuContent>
              <ActionButton href="/setting/portal">編集</ActionButton>
            </MenuItem>
            <MenuItem>
              <MenuContent>
                <MenuTitle>医師情報</MenuTitle>
                <MenuDescription>説明文</MenuDescription>
              </MenuContent>
              <ActionButton href="/setting/portal/staff">編集</ActionButton>
            </MenuItem>
          </SectionCard>
        </div>

        <div>
          <SectionTitle>各種設定リンク</SectionTitle>
          <SectionCard>
            <MenuItem>
              <MenuContent>
                <MenuTitle>診療科・診療メニュー設定</MenuTitle>
                <MenuDescription>説明文</MenuDescription>
              </MenuContent>
              <ActionButton href="/setting/dep">管理画面</ActionButton>
            </MenuItem>
            <MenuItem>
              <MenuContent>
                <MenuTitle>予約カレンダー管理</MenuTitle>
                <MenuDescription>説明文</MenuDescription>
              </MenuContent>
              <ActionButton href="/setting/calendar">管理画面</ActionButton>
            </MenuItem>
            <MenuItem>
              <MenuContent>
                <MenuTitle>決済サービス利用情報</MenuTitle>
                <MenuDescription>説明文</MenuDescription>
              </MenuContent>
              <ActionButton href="/setting/payment">管理画面</ActionButton>
            </MenuItem>
          </SectionCard>
        </div>
      </LeftContentContainer>
    </LeftContentWrapper>
  );
};
