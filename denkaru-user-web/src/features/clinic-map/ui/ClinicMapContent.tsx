import React from "react";

import { Layout } from "antd";
import styled from "styled-components";

import { PortalProvider } from "@/providers/PortalProvider";

import { LeftContent } from "./left-content";
import { RightContent } from "./right-content";

const ClinicMapContentContainer = styled(Layout.Content)`
  display: grid;
  grid-template-columns: 380px 1fr;
  gap: 20px;
  padding: 20px 20px 0px 0px;
`;

export const ClinicMapContent: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  return (
    <PortalProvider>
      <ClinicMapContentContainer>
        <LeftContent />
        <RightContent>{children}</RightContent>
      </ClinicMapContentContainer>
    </PortalProvider>
  );
};
