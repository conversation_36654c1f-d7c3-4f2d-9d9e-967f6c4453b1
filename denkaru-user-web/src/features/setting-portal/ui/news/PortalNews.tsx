import dayjs from "dayjs";
import { useRouter } from "next/router";
import styled from "styled-components";

import { Table } from "@/components/ui/Table";
import { formatYYYYMMDDHHmmWithSlash } from "@/utils/datetime-format";
import { Button } from "@/components/ui/NewButton";

import { useGetPortalNewsList } from "../../hooks/useGetPortalNewsList";

import type { GetPortalHospitalNotificationsQuery } from "@/apis/gql/operations/__generated__/portal-hospital-notification";
import type { TableColumnType } from "antd";

const PageWrapper = styled.div`
  width: 100%;
`;

const Wrapper = styled.div`
  height: calc(100% - 110px); // ヘッダーの高さ
  overflow-y: auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
`;

const PageTitle = styled.span`
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
`;

const StyledTable = styled(Table)`
  .ant-table-tbody > tr:not(.ant-table-measure-row) > td {
    border-right: 1.2px solid #e2e3e5;
    padding: 0px !important;
    line-height: 1;

    &:last-child {
      border-right: none;
    }
  }
  .ant-table-row {
    height: 52px;
  }
  .ant-table-tbody > tr:not(.ant-table-measure-row) > td:nth-child(4) {
    padding-left: 8px !important;
  }
  .inactive-row {
    background: #b2b2b2;
    border-color: #b2b2b2 !important;

    .ant-table-cell-row-hover {
      background: #b2b2b2 !important;
    }
  }
`;

const StyledButton = styled(Button)`
  width: 80px;
  height: 28px;
`;

export const PortalNews = () => {
  const { notifications } = useGetPortalNewsList();
  const { push } = useRouter();

  if (notifications === null || typeof notifications === "undefined") {
    return null;
  }

  const hospitalNotifications = notifications.hospitalNotifications;

  const columns: TableColumnType<
    NonNullable<
      GetPortalHospitalNotificationsQuery["getPortalHospitalNotifications"]["hospitalNotifications"]
    >[number]
  >[] = [
    {
      title: "開始日",
      align: "center",
      render: (_, { hospitalNotificationInfo: { startDate } }) => {
        return startDate ? formatYYYYMMDDHHmmWithSlash(startDate) : "-";
      },
    },
    {
      title: "終了日",
      align: "center",
      render: (_, { hospitalNotificationInfo: { endDate } }) => {
        return endDate ? formatYYYYMMDDHHmmWithSlash(endDate) : "-";
      },
    },
    {
      title: "状態",
      align: "center",
      render: (_, { hospitalNotificationInfo: { status } }) => {
        if (status === 0) return "下書き";
        if (status === 1) return "掲載中";
        if (status === 2) return "期間外";
        return null;
      },
    },
    {
      title: "タイトル",
      render: (_, { hospitalNotificationInfo: { title } }) => {
        return title;
      },
    },
    {
      title: "作成者",
      align: "center",
      render: (_, { hospitalNotificationCreateStaff }) => {
        if (!hospitalNotificationCreateStaff) return null;
        return hospitalNotificationCreateStaff.staffName;
      },
    },
    {
      title: "",
      align: "center",
      render: (_, { hospitalNotificationInfo: { hospitalNotificationId } }) => (
        <StyledButton
          onClick={() =>
            push(`/setting/portal/news/${hospitalNotificationId}/edit`)
          }
          varient="standard"
        >
          編集
        </StyledButton>
      ),
    },
  ];

  const dateSource = (hospitalNotifications ?? [])
    .map((notification) => ({
      key: notification.hospitalNotificationInfo.hospitalNotificationId,
      ...notification,
    }))
    .sort((a, b) => {
      const prev = dayjs(a.hospitalNotificationInfo.startDate);
      const curr = dayjs(b.hospitalNotificationInfo.startDate);

      if (!prev.isValid()) {
        return 1;
      }

      if (!curr.isValid()) {
        return -1;
      }

      if (prev.isBefore(curr)) {
        return 1;
      }

      if (prev.isAfter(curr)) {
        return -1;
      }

      return 0;
    });

  return (
    <PageWrapper>
      <Header>
        <PageTitle>掲載中のお知らせ</PageTitle>
        <Button
          varient="inline"
          onClick={() => push("/setting/portal/news/create")}
        >
          新規登録
        </Button>
      </Header>
      <Wrapper>
        <StyledTable
          columns={columns}
          dataSource={dateSource}
          rowClassName={(record) => {
            return record.hospitalNotificationInfo.status !== 1
              ? "inactive-row"
              : "";
          }}
        />
      </Wrapper>
    </PageWrapper>
  );
};
