import { type RefObject } from "react";

import { ContentLoading } from "@/components/ui/ContentLoading";

import { usePortalStaffEdit } from "../../hooks/usePortalStaffEdit";

import { PortalStaffEditForm } from "./PortalStaffEditForm";

type Props = {
  iframeRef?: RefObject<HTMLIFrameElement | null>;
  onTogglePreview?: () => void;
  previewKey?: number;
};

export const PortalStaffEdit: React.FC<Props> = ({
  iframeRef,
  onTogglePreview,
  previewKey,
}) => {
  const { loading, staffInfo, hospital } = usePortalStaffEdit();

  if (loading) {
    return <ContentLoading />;
  }

  if (
    staffInfo === null ||
    typeof staffInfo === "undefined" ||
    hospital === null ||
    typeof hospital === "undefined"
  ) {
    return null;
  }

  return (
    <PortalStaffEditForm
      staffInfo={staffInfo}
      isActive={hospital.isActive}
      iframeRef={iframeRef}
      onTogglePreview={onTogglePreview}
      previewKey={previewKey}
      hospital={hospital}
    />
  );
};
