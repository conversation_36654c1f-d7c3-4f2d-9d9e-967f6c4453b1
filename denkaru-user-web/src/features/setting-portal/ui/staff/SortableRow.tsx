import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { styled } from "styled-components";

import { SvgIconDragHundle } from "@/components/ui/Icon/IconDragHundle";
import { Button } from "@/components/ui/NewButton";

const Wrapper = styled.div`
  width: 100%;
  display: flex;
  height: 52px;
  align-items: center;
  padding: 12px 20px;
  border: solid 1px #e2e3e5;
  border-right: none;
  border-left: none;
  background-color: #fff;
`;

const Title = styled.span`
  width: 100%;
`;
const Actions = styled.span`
  position: relative;
  display: flex;
  gap: 16px;
`;

const IconWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
`;

const StyledButton = styled(Button)`
  width: 80px;
  height: 28px;
  font-weight: normal;
  &:hover {
    border: 1px solid #79d4ed !important;
  }
`;

type Props = {
  item: {
    key: number;
    name: string;
    order: number;
  };
  onEdit: () => void;
};

export const SortableRow: React.FC<Props> = ({ item, onEdit }) => {
  const {
    isDragging,
    attributes,
    setNodeRef,
    transform,
    transition,
    listeners,
  } = useSortable({
    id: item.key,
  });

  return (
    <Wrapper
      ref={setNodeRef}
      style={{
        transform: CSS.Transform.toString(
          transform && { ...transform, scaleY: 1 },
        ),
        transition,
        ...(isDragging ? { position: "relative", zIndex: 99 } : {}),
      }}
      {...attributes}
    >
      <Title>{item.name}</Title>
      <Actions>
        <StyledButton varient="standard" onClick={onEdit}>
          編集
        </StyledButton>

        <IconWrapper
          style={{ cursor: isDragging ? "grabbing" : "grab" }}
          {...listeners}
        >
          <SvgIconDragHundle />
        </IconWrapper>
      </Actions>
    </Wrapper>
  );
};
