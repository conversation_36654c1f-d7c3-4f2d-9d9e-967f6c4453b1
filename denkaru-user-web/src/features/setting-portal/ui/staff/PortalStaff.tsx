import { useRouter } from "next/router";
import styled from "styled-components";
import { Flex } from "antd";

import { Button } from "@/components/ui/NewButton";

import { useGetPortalStaff } from "../../hooks/useGetPortalStaff";

import { StaffInfoList } from "./StaffInfoList";

const PageWrapper = styled.div`
  width: 100%;
  font-size: 14px;
`;

const Wrapper = styled.div`
  height: calc(100% - 110px); // ヘッダーの高さ
  overflow-y: auto;
`;

const Heading = styled.div`
  padding: 15.5px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #6a757d;

  p {
    font-size: 16px;
    font-weight: bold;
    line-height: 1;
    color: #243544;
  }
`;

const StyledButton = styled(Button)`
  font-size: 14px;
  font-weight: normal;
  line-height: 1;
`;

export const PortalStaff = () => {
  const { staffs } = useGetPortalStaff();
  const { push } = useRouter();

  if (staffs === null || typeof staffs === "undefined") {
    return null;
  }

  return (
    <PageWrapper>
      <Wrapper>
        <Heading>
          <Flex align="center" gap={20}>
            <p>医師情報</p>
            <div>
              <span>
                GMOクリニック・マップでは、以下の表示順で医師情報が表示されます。
                <br />
                ドラッグ＆ドロップで順序を入れ替えることができます。
              </span>
            </div>
          </Flex>

          <StyledButton
            varient="inline"
            onClick={() =>
              push({
                pathname: "/setting/portal/staff/create",
              })
            }
          >
            新規登録
          </StyledButton>
        </Heading>

        <StaffInfoList
          items={staffs.map((staff) => ({
            key: staff.hospitalStaffId,
            name: staff.name,
            order: staff.order,
          }))}
        />
      </Wrapper>
    </PageWrapper>
  );
};
