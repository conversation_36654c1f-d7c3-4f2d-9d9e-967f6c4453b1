import React, { useCallback, useEffect, useState } from "react";

import {
  closestCenter,
  DndContext,
  PointerSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  rectSortingStrategy,
  SortableContext,
} from "@dnd-kit/sortable";
import { useRouter } from "next/router";
import { styled } from "styled-components";

import { useSortPortalStaff } from "../../hooks/useSortPortalStaff";

import { SortableRow } from "./SortableRow";

import type { DragEndEvent } from "@dnd-kit/core";

const NoItemsArea = styled.div`
  background-color: #fff;
  color: rgba(0, 0, 0, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 160px;
`;

type Props = {
  items: {
    key: number;
    name: string;
    order: number;
  }[];
};

export const StaffInfoList: React.FC<Props> = ({ items }) => {
  const [listItem, setListItem] = useState<
    {
      key: number;
      name: string;
      order: number;
    }[]
  >([]);
  const [changeIds, setChangeIds] = useState<number[]>([]);
  const { sortStaff } = useSortPortalStaff();
  const { push } = useRouter();

  const sensors = useSensors(useSensor(PointerSensor), useSensor(TouchSensor));

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    if (!over) return;

    if (active.id !== over?.id) {
      setListItem((prevItems) => {
        const oldIndex = prevItems.findIndex((item) => item.key === active.id);
        const newIndex = prevItems.findIndex((item) => item.key === over!.id);

        const tempItem = arrayMove(prevItems, oldIndex, newIndex);
        const newItems = [] as Array<{
          key: number;
          name: string;
          order: number;
        }>;

        for (let index = 0; index < tempItem.length; index += 1) {
          const item = tempItem[index];
          const prevSchema = prevItems[index];
          if (prevSchema && item) {
            newItems.push({
              ...item,
            });
          } else if (item) {
            newItems.push(item);
          }
        }
        setChangeIds(newItems.map((item) => item.key));
        return newItems;
      });
    }
  }, []);

  useEffect(() => {
    setListItem(items.sort((a, b) => a.order - b.order));
  }, [items]);

  useEffect(() => {
    if (changeIds.length > 0) {
      sortStaff(changeIds);
      setChangeIds([]);
    }
  }, [changeIds, sortStaff]);

  return (
    <div>
      {!items.length ? (
        <NoItemsArea>検索結果がございません。</NoItemsArea>
      ) : (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          modifiers={[restrictToVerticalAxis]}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={listItem.map((item) => item.key)}
            strategy={rectSortingStrategy}
          >
            {listItem.map((item) => (
              <SortableRow
                key={item.key}
                item={item}
                onEdit={() =>
                  push({
                    pathname: `/setting/portal/staff/${item.key}/edit`,
                  })
                }
              />
            ))}
          </SortableContext>
        </DndContext>
      )}
    </div>
  );
};
