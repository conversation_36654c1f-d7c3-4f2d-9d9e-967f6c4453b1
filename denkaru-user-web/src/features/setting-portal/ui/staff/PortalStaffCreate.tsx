import { type RefObject } from "react";

import { ContentLoading } from "@/components/ui/ContentLoading";

import { usePortalStaffCreate } from "../../hooks/usePortalStaffCreate";

import { PortalStaffCreateForm } from "./PortalStaffCreateForm";

type Props = {
  iframeRef?: RefObject<HTMLIFrameElement | null>;
  onTogglePreview?: () => void;
  previewKey?: number;
};

export const PortalStaffCreate: React.FC<Props> = ({
  iframeRef,
  onTogglePreview,
  previewKey,
}) => {
  const { loading, hospital } = usePortalStaffCreate();

  if (loading) {
    return <ContentLoading />;
  }

  if (hospital === null || typeof hospital === "undefined") {
    return null;
  }

  return (
    <PortalStaffCreateForm
      isActive={hospital.isActive}
      iframeRef={iframeRef}
      onTogglePreview={onTogglePreview}
      previewKey={previewKey}
      hospital={hospital}
    />
  );
};
