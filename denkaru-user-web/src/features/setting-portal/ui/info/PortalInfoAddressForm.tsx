import { Flex } from "antd";
import { Controller } from "react-hook-form";
import styled from "styled-components";

import { ErrorText as CommonErrorText } from "@/components/ui/ErrorText";
import { InputLabel } from "@/components/ui/InputLabel";
import { Pulldown } from "@/components/ui/Pulldown";
import { TextInput } from "@/components/ui/TextInput";
import { POSTCODE_REGEXP } from "@/constants/validation";
import { Button } from "@/components/ui/NewButton";

import { useSearchAddress } from "../../hooks/useSearchAddress";

import type { Control, FieldErrors, UseFormClearErrors } from "react-hook-form";
import type { TypePortalInfoForm } from "../../types";

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const InputWrapper = styled.div`
  margin-bottom: 20px;
`;

const StyledPulldown = styled(Pulldown)`
  width: 520px;
  margin-bottom: 8px;
`;

const ErrorText = styled(CommonErrorText)`
  margin-top: 4px;
`;

const StyledButton = styled(Button)`
  width: 80px;
  height: 36px;
  &:hover {
    border: 1px solid #79d4ed !important;
  }
`;

const StyledTextInput = styled(TextInput)<{ $width: number }>`
  display: block;
  width: ${({ $width }) => `${$width}px`};
`;

type Props = {
  defaultPostcode: string;
  control: Control<TypePortalInfoForm>;
  errors: FieldErrors<TypePortalInfoForm>;
  clearErrors: UseFormClearErrors<TypePortalInfoForm>;
  handleResetFormAddress1: (address1: string) => void;
  handleResetFormAddress2: (address2: string) => void;
  currentPostcode: string;
  currentAddress1: string;
};

export const PortalInfoAddressForm: React.FC<Props> = ({
  defaultPostcode,
  control,
  errors,
  handleResetFormAddress1,
  handleResetFormAddress2,
  clearErrors,
  currentPostcode,
  currentAddress1,
}) => {
  const {
    isSearched,
    addressList,
    handleGetAddressList,
    resetAddressList,
    resetAddressSearched,
  } = useSearchAddress(defaultPostcode, handleResetFormAddress1);

  // 郵便番号・住所1・住所2周りをリセットする処理
  const resetAddressAllParams = () => {
    resetAddressSearched();
    resetAddressList();
    clearErrors("postCode");
    handleResetFormAddress1("");
    handleResetFormAddress2("");
  };

  return (
    <>
      <InputWrapper>
        <StyledLabel label="郵便番号" required />
        <Flex gap={12} align="flex-end">
          <Controller
            name="postCode"
            control={control}
            rules={{
              required: "郵便番号を入力してください",
              pattern: {
                value: POSTCODE_REGEXP,
                message: "郵便番号形式で入力してください",
              },
              validate: (homePost) => {
                if (homePost.match(POSTCODE_REGEXP) && !currentAddress1) {
                  return "住所検索から住所を選択してください";
                }
                return true;
              },
            }}
            render={({ field: { value, onChange } }) => (
              <StyledTextInput
                placeholder="0011234"
                value={value}
                $width={120}
                onChange={(e) => {
                  if (isSearched) {
                    // すでに検索済みの状態で入力に変更があった場合は初期化
                    resetAddressAllParams();
                  }
                  onChange(e);
                }}
                hasError={!!errors.postCode}
              />
            )}
          />
          <StyledButton
            varient="standard-sr"
            onClick={() => {
              // 検索するタイミングで住所情報を初期化
              resetAddressAllParams();
              handleGetAddressList(currentPostcode);
            }}
          >
            住所検索
          </StyledButton>
        </Flex>
        {errors.postCode && <ErrorText>{errors.postCode.message}</ErrorText>}
      </InputWrapper>

      {(isSearched || !!currentAddress1) && (
        <InputWrapper>
          <StyledLabel label="住所" required />
          <Controller
            name="address1"
            control={control}
            render={({ field }) => (
              <StyledPulldown
                {...field}
                options={addressList.map((address) => ({
                  label: address.address,
                  value: address.address,
                }))}
                disabled={!addressList.length && !currentAddress1}
              />
            )}
          />

          <Controller
            name="address2"
            control={control}
            render={({ field }) => (
              <StyledTextInput
                {...field}
                hasError={!!errors.address2}
                $width={520}
                shouldTrim
              />
            )}
            rules={{
              maxLength: {
                value: 100,
                message: "100文字以内で入力してください。",
              },
            }}
          />
          {errors.address2 && <ErrorText>{errors.address2.message}</ErrorText>}
        </InputWrapper>
      )}
    </>
  );
};
