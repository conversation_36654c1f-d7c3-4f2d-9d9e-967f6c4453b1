import { Flex, TimePicker } from "antd";
import dayjs from "dayjs";
import { Controller } from "react-hook-form";
import styled from "styled-components";
import { v4 as uuid } from "uuid";

import { ErrorText as CommonErrorText } from "@/components/ui/ErrorText";
import { SvgIconDelete } from "@/components/ui/Icon/IconDelete";
import { IconButton } from "@/components/ui/IconButton";
import { InputLabel } from "@/components/ui/InputLabel";
import { Button } from "@/components/ui/NewButton";
import { Table } from "@/components/ui/Table";

import { SelectDayTypeDropdown } from "./SelectDayTypeDropdown";

import type { TypePortalInfoForm } from "../../types";
import type { Control, FieldErrors } from "react-hook-form";

const InputWrapper = styled.div`
  margin-bottom: 20px;
`;

const StyledTable = styled(Table)`
  .ant-table-row {
    background-color: #f1f4f7;
  }
`;

const StyledButton = styled(Button)`
  margin-top: 12px;
  &:hover {
    border: 1px solid #79d4ed !important;
  }
`;

const StyledIconButton = styled(IconButton)`
  background-color: transparent;
`;

const StyledTimePicker = styled(TimePicker)`
  width: 100px;
  .ant-picker-suffix {
    display: none;
  }
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 4px;
`;

const ErrorText = styled(CommonErrorText)`
  margin-top: 8px;
`;

type Props = {
  control: Control<TypePortalInfoForm>;
  errors: FieldErrors<TypePortalInfoForm>;
};

// HH:mm形式の文字列をdayjs.Dayjsオブジェクトに変換
const parseTimeStrToDayjsObj = (timeStr: string) => {
  const [hour, minute] = timeStr.split(":");

  if (typeof hour === "undefined" || typeof minute === "undefined") {
    throw new Error("failed to parse HH:mm to dayjs.Dayjs object");
  }

  return dayjs().hour(Number(hour)).minute(Number(minute));
};

export const PortalInfoBusinessTimeForm: React.FC<Props> = ({
  control,
  errors,
}) => {
  return (
    <InputWrapper>
      <Controller
        name="businessTimes"
        control={control}
        rules={{
          validate: (businessTimes) => {
            // 開始・終了・最終受付の整合性チェック
            const validateMsgArray: string[] = businessTimes
              .map((businessTime) => {
                const { startTime, endTime } = businessTime;
                const startTimeValue = parseTimeStrToDayjsObj(startTime);
                const endTimeValue = parseTimeStrToDayjsObj(endTime);

                // 終了が開始より後かどうか
                if (!endTimeValue.isAfter(startTimeValue)) {
                  return "終了時間が開始時間より前にある設定があります";
                }

                return "";
              })
              .filter((msg) => msg !== "");

            return !validateMsgArray.length ? true : validateMsgArray[0];
          },
        }}
        render={({ field: { value, onChange } }) => (
          <>
            <StyledTable
              dataSource={value.map((time) => ({
                key: time.businessTimeId,
                ...time,
              }))}
              columns={[
                {
                  title: "診療時間",
                  render: (_, { startTime, endTime, businessTimeId }) => {
                    const startTimeValue = parseTimeStrToDayjsObj(startTime);
                    const endTimeValue = parseTimeStrToDayjsObj(endTime);

                    return (
                      <Flex gap={20}>
                        <div>
                          <StyledLabel label="開始" />
                          <StyledTimePicker
                            value={startTimeValue}
                            showHour
                            showMinute
                            showNow={false}
                            minuteStep={30}
                            allowClear={false}
                            changeOnScroll
                            needConfirm={false}
                            onChange={(input) => {
                              onChange(
                                value.map((curr) => {
                                  if (curr.businessTimeId !== businessTimeId) {
                                    return curr;
                                  }
                                  return {
                                    ...curr,
                                    startTime: input.format("HH:mm"),
                                  };
                                }),
                              );
                            }}
                          />
                        </div>
                        <div>
                          <StyledLabel label="終了" />
                          <StyledTimePicker
                            value={endTimeValue}
                            showHour
                            showMinute
                            showNow={false}
                            minuteStep={30}
                            allowClear={false}
                            changeOnScroll
                            needConfirm={false}
                            onChange={(input) => {
                              onChange(
                                value.map((curr) => {
                                  if (curr.businessTimeId !== businessTimeId) {
                                    return curr;
                                  }
                                  return {
                                    ...curr,
                                    endTime: input.format("HH:mm"),
                                  };
                                }),
                              );
                            }}
                          />
                        </div>
                      </Flex>
                    );
                  },
                },
                {
                  title: "曜日",
                  render: (
                    _,
                    {
                      monFlag,
                      tueFlag,
                      wedFlag,
                      thuFlag,
                      friFlag,
                      satFlag,
                      sunFlag,
                      businessTimeId,
                    },
                  ) => (
                    <Flex gap={4}>
                      <SelectDayTypeDropdown
                        label="月"
                        value={monFlag}
                        onChange={(input) => {
                          onChange(
                            value.map((curr) => {
                              if (curr.businessTimeId !== businessTimeId) {
                                return curr;
                              }
                              return {
                                ...curr,
                                monFlag: input,
                              };
                            }),
                          );
                        }}
                      />
                      <SelectDayTypeDropdown
                        label="火"
                        value={tueFlag}
                        onChange={(input) => {
                          onChange(
                            value.map((curr) => {
                              if (curr.businessTimeId !== businessTimeId) {
                                return curr;
                              }
                              return {
                                ...curr,
                                tueFlag: input,
                              };
                            }),
                          );
                        }}
                      />
                      <SelectDayTypeDropdown
                        label="水"
                        value={wedFlag}
                        onChange={(input) => {
                          onChange(
                            value.map((curr) => {
                              if (curr.businessTimeId !== businessTimeId) {
                                return curr;
                              }
                              return {
                                ...curr,
                                wedFlag: input,
                              };
                            }),
                          );
                        }}
                      />
                      <SelectDayTypeDropdown
                        label="木"
                        value={thuFlag}
                        onChange={(input) => {
                          onChange(
                            value.map((curr) => {
                              if (curr.businessTimeId !== businessTimeId) {
                                return curr;
                              }
                              return {
                                ...curr,
                                thuFlag: input,
                              };
                            }),
                          );
                        }}
                      />
                      <SelectDayTypeDropdown
                        label="金"
                        value={friFlag}
                        onChange={(input) => {
                          onChange(
                            value.map((curr) => {
                              if (curr.businessTimeId !== businessTimeId) {
                                return curr;
                              }
                              return {
                                ...curr,
                                friFlag: input,
                              };
                            }),
                          );
                        }}
                      />
                      <SelectDayTypeDropdown
                        label="土"
                        value={satFlag}
                        onChange={(input) => {
                          onChange(
                            value.map((curr) => {
                              if (curr.businessTimeId !== businessTimeId) {
                                return curr;
                              }
                              return {
                                ...curr,
                                satFlag: input,
                              };
                            }),
                          );
                        }}
                      />
                      <SelectDayTypeDropdown
                        label="日"
                        value={sunFlag}
                        onChange={(input) => {
                          onChange(
                            value.map((curr) => {
                              if (curr.businessTimeId !== businessTimeId) {
                                return curr;
                              }
                              return {
                                ...curr,
                                sunFlag: input,
                              };
                            }),
                          );
                        }}
                      />
                    </Flex>
                  ),
                },
                {
                  title: "",
                  width: 80,
                  render: (_, { businessTimeId }) => {
                    if (value.length <= 1) return null;
                    return (
                      <StyledIconButton
                        varient="icon-only"
                        icon={<SvgIconDelete />}
                        onClick={() =>
                          onChange(
                            value.filter(
                              (curr) => curr.businessTimeId !== businessTimeId,
                            ),
                          )
                        }
                      />
                    );
                  },
                },
              ]}
              noDataText="診療時間が設定されていません"
            />
            <StyledButton
              varient="ordinary"
              onClick={() => {
                const id = uuid();
                onChange([
                  ...value,
                  {
                    key: id,
                    businessTimeId: id,
                    startTime: "09:00",
                    endTime: "18:00",
                    monFlag: 0,
                    tueFlag: 0,
                    wedFlag: 0,
                    thuFlag: 0,
                    friFlag: 0,
                    satFlag: 0,
                    sunFlag: 0,
                  },
                ]);
              }}
            >
              診療時間追加
            </StyledButton>
          </>
        )}
      />
      {errors.businessTimes && (
        <ErrorText>{errors.businessTimes.message}</ErrorText>
      )}
    </InputWrapper>
  );
};
