import { useMemo, useState, useEffect } from "react";

import styled from "styled-components";

import { encodeHpId } from "@/utils/common-helper";
import { SvgIphoneFrame } from "@/components/ui/Icon/IphoneFrame";
import { Button } from "@/components/ui/NewButton";
import { usePortal } from "@/providers/PortalProvider";
// eslint-disable-next-line import/no-restricted-paths
import { HospitalInfoPageType } from "@/features/start/types";
import { useGetApiKaGetListMstQuery } from "@/apis/gql/operations/__generated__/ka";

import { usePortalPreview } from "../../hooks/usePortalPreview";
import { useGetPortalStaff } from "../../hooks/useGetPortalStaff";
import { useGetPortalNewsList } from "../../hooks/useGetPortalNewsList";

import type { GetPortalHospitalQuery } from "@/apis/gql/operations/__generated__/portal-hospital";
import type { RefObject } from "react";

type Props = {
  hospital:
    | NonNullable<
        GetPortalHospitalQuery["getPortalHospitalById"]["portalHospital"]
      >
    | null
    | undefined;
  iframeRef: RefObject<HTMLIFrameElement>;
  isPreviewing: boolean;
  previewKey: number;
  previewData?: unknown; // Add previewData prop
};

const MobilePreviewSection = styled.div`
  flex: 1;
  width: 620px;
  background: #e0e6ec;
  border-radius: 12px;
  padding: 36px 0px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
`;

const IframeStyled = styled.iframe`
  width: 428px;
  height: 844px;
  margin-left: 10px;
  border: none;
  border-radius: 70px;
  background-color: #ffffff;
  transform: scale(0.84);
  transform-origin: top left;
  padding: 80px 4px 35px 10px;
`;

const IphoneFrameContainer = styled.div`
  position: relative;
  overflow: hidden;
  border-radius: 44px;
`;

const CustomSvgIphoneFrame = styled(SvgIphoneFrame)`
  width: 375px;
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none; /* Allows interaction with the iframe below */
`;

const MobilePreviewContainer = styled.div`
  position: relative;
  width: 375px;
  height: 550px;
`;

const StyledButton = styled(Button)`
  position: absolute;
  top: 20px;
  right: 20px;
`;

export const MobilePreview: React.FC<Props> = ({
  hospital,
  iframeRef,
  previewKey,
  previewData,
}) => {
  const baseUrl = process.env.NEXT_PUBLIC_BOOKING_CLIENT_URL;

  const { staffs } = useGetPortalStaff();
  const { tags, examination, specialist } = usePortal();
  const { data: treatmentCategories } = useGetApiKaGetListMstQuery({
    variables: {
      isDeleted: 0,
    },
    fetchPolicy: "no-cache",
  });
  const { notifications } = useGetPortalNewsList();

  const { postData, isConnected } = usePortalPreview(iframeRef, previewKey);
  const [shouldPostData, setShouldPostData] = useState(true);

  // Send initial hospital data when iframe is connected
  useEffect(() => {
    if (shouldPostData && isConnected && hospital) {
      postData({
        pageType: HospitalInfoPageType.TOP,
        ...hospital,
        stations: (hospital.hospitalStations ?? []).map((station) => ({
          stationDetail: {
            name: `${station.stationName}駅 徒歩 ${station.walkingMinute}分`,
          },
        })),
        examinations: (hospital.examinations ?? []).map(({ examinationId }) => {
          const exam = examination.find(
            (exam) => exam.examinationId === examinationId,
          );
          return {
            examinationId,
            name: exam?.name ?? "",
            type: exam?.type,
          };
        }),
        tags: (hospital.tags ?? []).map(({ tagId }) => {
          const tag = tags.find((tag) => tag.tagId === tagId);
          return {
            tagId,
            name: tag?.name ?? "",
          };
        }),
        pictures: hospital.files?.map((file, index) => ({
          fileId: index,
          pictureDetail: {
            filepath: file.s3Key,
          },
        })),
        specialists: (hospital.specialists ?? []).map(({ specialistId }) => {
          const spec = specialist.find(
            (spec) => spec.specialistId === specialistId,
          );
          return {
            specialistId,
            name: spec?.name ?? "",
          };
        }),
        hospitalStaffs: staffs ?? [],
        treatmentCategories: (
          treatmentCategories?.getApiKaGetListMst?.data?.departments ?? []
        ).map(({ kaName, id }) => ({
          name: kaName,
          treatmentCategoryId: id,
        })),
        notifications: (notifications?.hospitalNotifications ?? [])
          .filter(
            (notification) =>
              notification.hospitalNotificationInfo.status === 1,
          )
          .map((notification) => ({
            hospitalNotificationId:
              notification.hospitalNotificationInfo.hospitalNotificationId,
            title: notification.hospitalNotificationInfo.title,
            startDate: notification.hospitalNotificationInfo.startDate,
          })),
      });
      setShouldPostData(false);
    }
  }, [
    shouldPostData,
    isConnected,
    hospital,
    staffs,
    postData,
    tags,
    examination,
    specialist,
    treatmentCategories,
    notifications,
  ]);

  // Send preview data when it changes (from handlePreview)
  useEffect(() => {
    if (isConnected && previewData) {
      postData(previewData);
    }
  }, [isConnected, previewData, postData]);
  const encodedId = encodeHpId(hospital?.hospitalId ?? 0);

  const clinicLink = useMemo(() => {
    if (!baseUrl || !hospital?.hospitalId) return "";
    const path = `/hospital/${encodedId}/preview`;
    return `${baseUrl}${path}`;
  }, [hospital, baseUrl, encodedId]);

  return (
    <MobilePreviewSection>
      <StyledButton
        varient="secondary"
        onClick={() => {
          window.open(`${baseUrl}/hospital/${encodedId}`, "_blank");
        }}
      >
        掲載ページ確認
      </StyledButton>
      <MobilePreviewContainer>
        <IphoneFrameContainer>
          <IframeStyled ref={iframeRef} src={clinicLink} key={previewKey} />
          <CustomSvgIphoneFrame />
        </IphoneFrameContainer>
      </MobilePreviewContainer>
    </MobilePreviewSection>
  );
};
