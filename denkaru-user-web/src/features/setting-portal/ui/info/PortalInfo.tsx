import { type RefObject } from "react";

import styled from "styled-components";

import { useGetPortalHospital } from "../../hooks/useGetPortalHospital";

import { PortalInfoForm } from "./PortalInfoForm";

import type { GetExaminationsQuery } from "@/apis/gql/operations/__generated__/examination";
import type { GetPortalHospitalQuery } from "@/apis/gql/operations/__generated__/portal-hospital";
import type { GetSpecialistsQuery } from "@/apis/gql/operations/__generated__/specialist";
import type { GetTagsQuery } from "@/apis/gql/operations/__generated__/tag";

const PageWrapper = styled.div`
  width: 100%;
  position: relative;
`;

const Wrapper = styled.div`
  position: relative;
  font-size: 14px;
`;

type Props = {
  hospital: NonNullable<
    GetPortalHospitalQuery["getPortalHospitalById"]["portalHospital"]
  > | null;
  tags: NonNullable<GetTagsQuery["getTags"]>;
  examination: NonNullable<GetExaminationsQuery["getExaminations"]>;
  specialist: NonNullable<GetSpecialistsQuery["getSpecialists"]>;
  iframeRef?: RefObject<HTMLIFrameElement | null>;
  onTogglePreview?: () => void;
  onPreview?: (data: unknown) => void;
  previewKey?: number;
};

export const PortalInfo: React.FC<Props> = ({
  hospital,
  tags,
  examination,
  specialist,
  iframeRef,
  onTogglePreview,
  onPreview,
  previewKey,
}) => {
  const { hasError, scuelData } = useGetPortalHospital();

  if (
    hasError ||
    tags === null ||
    typeof tags === "undefined" ||
    examination === null ||
    typeof examination === "undefined" ||
    specialist === null ||
    typeof specialist === "undefined"
  ) {
    return null;
  }

  // 新規作成時の初期データ
  const initialHospitalData: NonNullable<
    GetPortalHospitalQuery["getPortalHospitalById"]["portalHospital"]
  > = {
    hospitalId: 0,
    name: "",
    postCode: "",
    telephone: "",
    address1: "",
    address2: "",
    isCarpark: false,
    paymentDetails: "",
    descriptionTitle: "",
    description: "",
    accessDetail: "",
    homePage: "",
    isActive: false,
    tags: [],
    businessTimes: [],
    examinations: [],
    specialists: [],
    directorName: "",
    carparkDetail: "",
    timelineDescription: "",
    holidayDetail: "",
    hospitalStations: [],
    mailAddress: "",
    files: [],
  };

  return (
    <PageWrapper>
      <Wrapper>
        <PortalInfoForm
          hospital={hospital ?? initialHospitalData}
          tags={tags}
          examination={examination}
          specialist={specialist}
          iframeRef={iframeRef}
          onTogglePreview={onTogglePreview}
          onPreview={onPreview}
          previewKey={previewKey}
          scuelData={scuelData?.getImportDataByHpId}
        />
      </Wrapper>
    </PageWrapper>
  );
};
