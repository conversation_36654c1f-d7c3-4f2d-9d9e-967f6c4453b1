import { useState, type RefObject, useRef, useEffect } from "react";

import { Checkbox as AntdCheckbox, Radio as AntdRadio } from "antd";
import { Controller } from "react-hook-form";
import styled from "styled-components";

import { Form } from "@/components/functional/Form";
import { Checkbox } from "@/components/ui/Checkbox";
import { ErrorText as CommonErrorText } from "@/components/ui/ErrorText";
import { InputLabel } from "@/components/ui/InputLabel";
import { Loading } from "@/components/ui/Loading";
import { Button } from "@/components/ui/NewButton";
import { Radio } from "@/components/ui/Radio";
import { TextAreaInput } from "@/components/ui/TextAreaInput";
import { TextInput } from "@/components/ui/TextInput";
import { EMAIL_REGEXP, PHONE_REGEXP, URL_REGEXP } from "@/constants/validation";
// eslint-disable-next-line import/no-restricted-paths
import { HospitalInfoPageType } from "@/features/start/types";
import { useGetApiKaGetListMstQuery } from "@/apis/gql/operations/__generated__/ka";

import { useUpdateHospitalInfo } from "../../hooks/useUpdateHospitalInfo";
import { convertOSScuelDataToPortalDataForm } from "../../utils";
import { PortalConfirmModal } from "../PortalConfirmModal";
import { useGetPortalStaff } from "../../hooks/useGetPortalStaff";
import { useGetPortalNewsList } from "../../hooks/useGetPortalNewsList";

import { ClinicImageUploader } from "./ClinicImageUploader";
import { HomepageURLInputModal } from "./HomepageURLInputModal";
import { PortalInfoAddressForm } from "./PortalInfoAddressForm";
import { PortalInfoBusinessTimeForm } from "./PortalInfoBusinessTimeForm";
import { PortalInfoStationForm } from "./PortalInfoStationForm";

import type { GetExaminationsQuery } from "@/apis/gql/operations/__generated__/examination";
import type { GetImportDataByHpIdQuery } from "@/apis/gql/operations/__generated__/hospital";
import type { GetPortalHospitalQuery } from "@/apis/gql/operations/__generated__/portal-hospital";
import type { GetSpecialistsQuery } from "@/apis/gql/operations/__generated__/specialist";
import type { GetTagsQuery } from "@/apis/gql/operations/__generated__/tag";

const ScuelDataNote = styled.div`
  color: #1d578b;
  padding: 12px;
  margin-bottom: 20px;
  background-color: #e1f5fe;
  border: solid 1px #a1c5e9;
`;

const StyledButton = styled(Button)`
  width: 200px;
  margin-bottom: 20px;
  color: #243544;
`;

const Heading = styled.p`
  font-size: 16px;
  line-height: 16px;
  font-weight: bold;
  padding-bottom: 20px;

  &:first-child {
    padding: 20px;
    border-bottom: 1px solid #e0e6ec;
  }
`;

const FormContainer = styled.div`
  border-radius: 12px;
  overflow: hidden;
  position: relative;
`;

const FormWrapper = styled.div`
  padding: 20px 20px 76px 20px;
  max-height: calc(100vh - 145px);
  overflow-y: auto;
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const InputWrapper = styled.div`
  margin-bottom: 20px;
`;

const StyledTextAreaInput = styled(TextAreaInput)`
  width: 520px;
  height: 120px !important;
  resize: none !important;
`;

const StyledTextInput = styled(TextInput)<{ $width?: number }>`
  width: ${({ $width }) => $width || "520"}px;
`;

const StyledCheckboxGroup = styled(AntdCheckbox.Group)`
  display: flex;
  column-gap: 0;
  row-gap: 12px;

  .ant-checkbox-wrapper {
    width: 25%;
  }
`;

const StyledExamGroup = styled(AntdCheckbox.Group)`
  .ant-checkbox-wrapper {
    width: 25%;
  }
`;

const ExamCheckboxSection = styled.div`
  display: flex;
  flex-wrap: wrap;
  row-gap: 12px;
`;

const ButtonWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  background-color: #ffffff;
  height: 76px;
  border-top: 1px solid #e0e6ec;
  padding: 20px 24px;
  width: 100%;
  border-radius: 0 0 12px 12px;

  button:last-child {
    margin-left: 8px;
  }
`;

const HorizontalLine = styled.div`
  border: 2px solid #e0e6ec;
  margin: 20px 0;
`;

const ErrorText = styled(CommonErrorText)`
  margin-top: 6px;
`;

const LabelWithCounter = styled.div<{ $maxWidth?: number }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: ${({ $maxWidth }) => ($maxWidth ? `${$maxWidth}px` : "100%")};
`;

const CharacterCounter = styled.span<{ $isOverLimit?: boolean }>`
  font-size: 14px;
  margin-bottom: 6px;
  color: ${({ $isOverLimit }) => ($isOverLimit ? "#e74c3c" : "#6a757d")};
  font-weight: normal;
  line-height: 1;
`;

type Props = {
  hospital: NonNullable<
    GetPortalHospitalQuery["getPortalHospitalById"]["portalHospital"]
  >;
  tags: NonNullable<GetTagsQuery["getTags"]>;
  examination: NonNullable<GetExaminationsQuery["getExaminations"]>;
  specialist: NonNullable<GetSpecialistsQuery["getSpecialists"]>;
  iframeRef?: RefObject<HTMLIFrameElement | null>;
  onTogglePreview?: () => void;
  onPreview: (data: unknown) => void;
  previewKey?: number;
  scuelData:
    | NonNullable<GetImportDataByHpIdQuery["getImportDataByHpId"]>
    | undefined;
};

export const PortalInfoForm: React.FC<Props> = ({
  hospital,
  tags,
  examination,
  specialist,
  iframeRef,
  onTogglePreview,
  onPreview,
  previewKey,
  scuelData,
}) => {
  const {
    submitting,
    errors,
    onSubmit,
    control,
    clearErrors,
    handleResetFormAddress1,
    handleResetFormAddress2,
    currentPostcode,
    currentAddress1,
    isDirty,
    getValues,
    isValid,
    setPortalInfoFormValues,
    setDescriptionValues,
  } = useUpdateHospitalInfo(hospital);
  const { staffs } = useGetPortalStaff();
  const { data: treatmentCategories } = useGetApiKaGetListMstQuery({
    variables: {
      isDeleted: 0,
    },
    fetchPolicy: "no-cache",
  });
  const { notifications } = useGetPortalNewsList();

  const dummyRef = useRef<HTMLIFrameElement>(null);

  const examGroupA = examination.filter((exam) => exam.type === 2);
  const examGroupB = examination.filter((exam) => exam.type === 1);
  const formId = "edit-portal-info-form";
  const NAME_MAX_LENGTH = 30;

  const [shouldPostData, setShouldPostData] = useState(true);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);

  const handlePreview = () => {
    onTogglePreview?.();
    const formDatas = getValues();
    onPreview({
      pageType: HospitalInfoPageType.TOP,
      ...formDatas,
      examinations: (formDatas?.examinationIds ?? []).map((examinationId) => {
        const exam = examination.find(
          (exam) => exam.examinationId === examinationId,
        );
        return {
          examinationId,
          name: exam?.name ?? "",
          type: exam?.type,
        };
      }),
      stations: (formDatas?.hospitalStations ?? []).map((station) => ({
        stationDetail: {
          name: `${station.stationName}駅 徒歩 ${station.walkingMinute}分`,
        },
      })),
      tags: (formDatas?.tagIds ?? []).map((tagId) => {
        const tag = tags.find((tag) => tag.tagId === tagId);
        return {
          tagId,
          name: tag?.name ?? "",
        };
      }),
      pictures: formDatas?.files.map((file, index) => ({
        fileId: index,
        pictureDetail: {
          filepath: file.url || file.thumbUrl,
        },
      })),
      specialists: (formDatas?.specialistIds ?? []).map((specialistId) => {
        const spec = specialist.find(
          (spec) => spec.specialistId === specialistId,
        );
        return {
          specialistId,
          name: spec?.name ?? "",
        };
      }),
      hospitalStaffs: staffs ?? [],
      treatmentCategories: (
        treatmentCategories?.getApiKaGetListMst?.data?.departments ?? []
      ).map(({ kaName, id }) => ({
        name: kaName,
        treatmentCategoryId: id,
      })),
      notifications: (notifications?.hospitalNotifications ?? [])
        .filter(
          (notification) => notification.hospitalNotificationInfo.status === 1,
        )
        .map((notification) => ({
          hospitalNotificationId:
            notification.hospitalNotificationInfo.hospitalNotificationId,
          title: notification.hospitalNotificationInfo.title,
          startDate: notification.hospitalNotificationInfo.startDate,
        })),
    });
  };

  const [isHomepageURLModalOpen, setIsHomepageURLModalOpen] = useState(false);

  useEffect(() => {
    if (hospital.hospitalId || !scuelData) return;
    setPortalInfoFormValues(
      convertOSScuelDataToPortalDataForm(scuelData, examination, specialist),
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scuelData, examination, specialist]);

  return (
    <>
      {submitting && <Loading isLoading />}
      <FormContainer>
        {!hospital.hospitalId && scuelData && (
          <ScuelDataNote>
            GMOクリニック・マップで掲載されている情報を下書きとして表示しています。最新の情報と異なる点がございましたら、内容を修正のうえ「保存」ボタンを押してください。
          </ScuelDataNote>
        )}
        <Heading>基本情報</Heading>
        <FormWrapper>
          <Form id={formId} onSubmit={onSubmit} noValidate>
            <InputWrapper>
              <LabelWithCounter $maxWidth={520}>
                <StyledLabel label="クリニック名" required />
                <CharacterCounter
                  $isOverLimit={hospital.name.length > NAME_MAX_LENGTH}
                >
                  {hospital.name.length}/{NAME_MAX_LENGTH}
                </CharacterCounter>
              </LabelWithCounter>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <StyledTextInput
                    {...field}
                    shouldTrim
                    hasError={!!errors.name}
                    placeholder="予約サイトに表示したいクリニック名を入力してください。"
                  />
                )}
                rules={{
                  maxLength: {
                    value: NAME_MAX_LENGTH,
                    message: `${NAME_MAX_LENGTH}文字以内で入力してください。`,
                  },
                  required: "クリニック名を入力してください",
                }}
              />
              {errors.name && <ErrorText>{errors.name.message}</ErrorText>}
            </InputWrapper>

            <PortalInfoAddressForm
              defaultPostcode={hospital.postCode}
              control={control}
              errors={errors}
              clearErrors={clearErrors}
              handleResetFormAddress1={handleResetFormAddress1}
              handleResetFormAddress2={handleResetFormAddress2}
              currentPostcode={currentPostcode}
              currentAddress1={currentAddress1}
            />

            <InputWrapper>
              <StyledLabel label="電話番号" required />
              <Controller
                name="telephone"
                control={control}
                render={({ field }) => (
                  <StyledTextInput
                    {...field}
                    placeholder="0312341234"
                    $width={140}
                    hasError={!!errors.telephone}
                  />
                )}
                rules={{
                  required: "電話番号を入力してください",
                  pattern: {
                    value: PHONE_REGEXP,
                    message: "電話番号形式で入力してください",
                  },
                }}
              />
              {errors.telephone && (
                <ErrorText>{errors.telephone.message}</ErrorText>
              )}
            </InputWrapper>

            <InputWrapper>
              <StyledLabel label="院長名" required />
              <Controller
                name="directorName"
                control={control}
                render={({ field }) => (
                  <StyledTextInput
                    {...field}
                    shouldTrim
                    hasError={!!errors.directorName}
                    placeholder="院長名を入力してください"
                  />
                )}
                rules={{
                  required: "院長名を入力してください",
                  maxLength: {
                    value: 30,
                    message: "30文字以内で入力してください。",
                  },
                }}
              />
              {errors.directorName && (
                <ErrorText>{errors.directorName.message}</ErrorText>
              )}
            </InputWrapper>

            <InputWrapper>
              <StyledLabel label="ホームページURL" />
              <Controller
                name="homePage"
                control={control}
                render={({ field }) => (
                  <StyledTextInput
                    {...field}
                    placeholder="ホームページURLを入力してください"
                    hasError={!!errors.homePage}
                    shouldTrim
                  />
                )}
                rules={{
                  pattern: {
                    value: URL_REGEXP,
                    message: "ホームページURLは正しい形式で入力してください",
                  },
                }}
              />
              {errors.homePage && (
                <ErrorText>{errors.homePage.message}</ErrorText>
              )}
            </InputWrapper>

            <InputWrapper>
              <StyledLabel label="メールアドレス" />
              <Controller
                name="mailAddress"
                control={control}
                render={({ field }) => (
                  <StyledTextInput
                    {...field}
                    placeholder="メールアドレスを入力してください"
                    hasError={!!errors.mailAddress}
                  />
                )}
                rules={{
                  pattern: {
                    value: EMAIL_REGEXP,
                    message: "メールアドレスは正しい形式で入力してください",
                  },
                }}
              />
              {errors.mailAddress && (
                <ErrorText>{errors.mailAddress.message}</ErrorText>
              )}
            </InputWrapper>

            <PortalInfoStationForm control={control} errors={errors} />

            <InputWrapper>
              <StyledLabel label="アクセス" />
              <Controller
                name="accessDetail"
                control={control}
                render={({ field }) => (
                  <StyledTextAreaInput
                    {...field}
                    hasError={!!errors.accessDetail}
                  />
                )}
                rules={{
                  maxLength: {
                    value: 1000,
                    message: "1000文字以内で入力してください。",
                  },
                }}
              />
              {errors.accessDetail && (
                <ErrorText>{errors.accessDetail.message}</ErrorText>
              )}
            </InputWrapper>

            <HorizontalLine />

            <Heading>クリニック紹介</Heading>
            <StyledButton
              varient="standard-sr"
              onClick={() => {
                setIsHomepageURLModalOpen(true);
              }}
            >
              URLから下書きを作成
            </StyledButton>

            <InputWrapper>
              <StyledLabel label="見出し文" required />
              <Controller
                name="descriptionTitle"
                control={control}
                render={({ field }) => (
                  <StyledTextInput
                    {...field}
                    shouldTrim
                    placeholder="紹介文の簡潔なタイトルを入力してください。"
                    hasError={!!errors.descriptionTitle}
                  />
                )}
                rules={{
                  required: "見出し文は入力必須です",
                  maxLength: {
                    value: 50,
                    message: "50文字以内で入力してください。",
                  },
                }}
              />
              {errors.descriptionTitle && (
                <ErrorText>{errors.descriptionTitle.message}</ErrorText>
              )}
            </InputWrapper>

            <InputWrapper>
              <StyledLabel label="紹介文" />
              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <StyledTextAreaInput
                    {...field}
                    hasError={!!errors.description}
                  />
                )}
                rules={{
                  maxLength: {
                    value: 1000,
                    message: "1000文字以内で入力してください。",
                  },
                }}
              />
              {errors.description && (
                <ErrorText>{errors.description.message}</ErrorText>
              )}
            </InputWrapper>

            <InputWrapper>
              <StyledLabel label="紹介用画像" />
              <Controller
                name="files"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <ClinicImageUploader
                    files={value}
                    description="推奨画像サイズ1200x900ピクセル"
                    handleChangeEvent={onChange}
                    maxCount={5}
                  />
                )}
              />
              {errors.files && <ErrorText>{errors.files.message}</ErrorText>}
            </InputWrapper>

            <HorizontalLine />

            <Heading>診療時間・曜日設定</Heading>

            <PortalInfoBusinessTimeForm control={control} errors={errors} />

            <InputWrapper>
              <StyledLabel label="補足説明" />
              <Controller
                name="timelineDescription"
                control={control}
                render={({ field }) => (
                  <StyledTextAreaInput
                    {...field}
                    placeholder="診療時間、休診に関する補足説明を入力してください。"
                    hasError={!!errors.timelineDescription}
                  />
                )}
                rules={{
                  maxLength: {
                    value: 200,
                    message: "200文字以内で入力してください。",
                  },
                }}
              />
              {errors.timelineDescription && (
                <ErrorText>{errors.timelineDescription.message}</ErrorText>
              )}
            </InputWrapper>

            <InputWrapper>
              <StyledLabel label="休診日" />
              <Controller
                name="holidayDetail"
                control={control}
                render={({ field }) => (
                  <StyledTextInput
                    {...field}
                    hasError={!!errors.holidayDetail}
                  />
                )}
                rules={{
                  maxLength: {
                    value: 200,
                    message: "200文字以内で入力してください。",
                  },
                }}
              />
              {errors.holidayDetail && (
                <ErrorText>{errors.holidayDetail.message}</ErrorText>
              )}
            </InputWrapper>

            <HorizontalLine />

            <Heading>その他</Heading>

            <InputWrapper>
              <StyledLabel label="駐車場" required />
              <Controller
                name="isCarpark"
                control={control}
                render={({ field }) => (
                  <AntdRadio.Group {...field}>
                    <Radio value={true}>あり</Radio>
                    <Radio value={false}>なし</Radio>
                  </AntdRadio.Group>
                )}
              />
              {errors.isCarpark && (
                <ErrorText>{errors.isCarpark.message}</ErrorText>
              )}
            </InputWrapper>

            <InputWrapper>
              <StyledLabel label="駐車台数や注意書き" />
              <Controller
                name="carparkDetail"
                control={control}
                render={({ field }) => (
                  <StyledTextAreaInput
                    {...field}
                    placeholder="駐車台数や注意書きなどを入力してください。"
                    hasError={!!errors.carparkDetail}
                  />
                )}
                rules={{
                  maxLength: {
                    value: 200,
                    message: "200文字以内で入力してください。",
                  },
                }}
              />
              {errors.carparkDetail && (
                <ErrorText>{errors.carparkDetail.message}</ErrorText>
              )}
            </InputWrapper>

            <InputWrapper>
              <StyledLabel label="施設の特徴" />
              <Controller
                name="tagIds"
                control={control}
                render={({ field }) => (
                  <StyledCheckboxGroup {...field}>
                    {tags.map((tag) => (
                      <Checkbox key={tag.tagId} value={tag.tagId}>
                        {tag.name}
                      </Checkbox>
                    ))}
                  </StyledCheckboxGroup>
                )}
              />
              {errors.tagIds && <ErrorText>{errors.tagIds.message}</ErrorText>}
            </InputWrapper>

            <Controller
              name="examinationIds"
              control={control}
              render={({ field }) => (
                <StyledExamGroup {...field}>
                  <InputWrapper>
                    <StyledLabel label="検診・ドック" />
                    <ExamCheckboxSection>
                      {examGroupA.map((exam) => (
                        <Checkbox
                          key={exam.examinationId}
                          value={exam.examinationId}
                        >
                          {exam.name}
                        </Checkbox>
                      ))}
                    </ExamCheckboxSection>
                  </InputWrapper>

                  <InputWrapper>
                    <StyledLabel label="検査・設備" />
                    <ExamCheckboxSection>
                      {examGroupB.map((exam) => (
                        <Checkbox
                          key={exam.examinationId}
                          value={exam.examinationId}
                        >
                          {exam.name}
                        </Checkbox>
                      ))}
                    </ExamCheckboxSection>
                  </InputWrapper>
                </StyledExamGroup>
              )}
            />
            {errors.examinationIds && (
              <ErrorText>{errors.examinationIds.message}</ErrorText>
            )}

            <InputWrapper>
              <StyledLabel label="専門医" />
              <Controller
                name="specialistIds"
                control={control}
                render={({ field }) => (
                  <StyledCheckboxGroup {...field}>
                    {specialist.map((sp) => (
                      <Checkbox key={sp.specialistId} value={sp.specialistId}>
                        {sp.name}
                      </Checkbox>
                    ))}
                  </StyledCheckboxGroup>
                )}
              />
              {errors.specialistIds && (
                <ErrorText>{errors.specialistIds.message}</ErrorText>
              )}
            </InputWrapper>

            <InputWrapper>
              <StyledLabel label="決済方法" required />
              <Controller
                name="paymentDetails"
                control={control}
                render={({ field }) => (
                  <StyledTextAreaInput
                    {...field}
                    hasError={!!errors.paymentDetails}
                    placeholder={`クリニックで利用可能な決済方法を入力してください。\n例：当院では以下のクレジットカードをご利用いただけます。\nVISA、Master、JCB`}
                  />
                )}
                rules={{
                  required: "決済方法を入力してください",
                  maxLength: {
                    value: 200,
                    message: "200文字以内で入力してください。",
                  },
                }}
              />
              {errors.paymentDetails && (
                <ErrorText>{errors.paymentDetails.message}</ErrorText>
              )}
            </InputWrapper>

            <ButtonWrapper>
              <Button
                varient="secondary"
                onClick={handlePreview}
                disabled={!isDirty || !isValid}
              >
                プレビュー表示
              </Button>
              <Button
                varient="primary"
                shape="round"
                disabled={submitting}
                htmlType={hospital.isActive ? "button" : "submit"}
                onClick={() => {
                  if (!hospital.isActive) {
                    return;
                  }
                  setIsConfirmOpen(true);
                }}
              >
                保存
              </Button>
            </ButtonWrapper>

            <PortalConfirmModal
              formId={formId}
              title="GMOクリニック・マップ連携情報の編集"
              isOpen={isConfirmOpen}
              onClose={() => setIsConfirmOpen(false)}
              isSubmitting={submitting}
            />
          </Form>
        </FormWrapper>
      </FormContainer>
      <HomepageURLInputModal
        onClose={() => setIsHomepageURLModalOpen(false)}
        isOpen={isHomepageURLModalOpen}
        onComplete={(value) => {
          setDescriptionValues(value.descriptionTitle, value.description);
        }}
      />
    </>
  );
};
