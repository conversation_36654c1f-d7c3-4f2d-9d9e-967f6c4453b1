import { useState } from "react";

import { Flex } from "antd";
import { Controller } from "react-hook-form";
import styled from "styled-components";

import { ErrorText as CommonErrorText } from "@/components/ui/ErrorText";
import { SvgIconDelete } from "@/components/ui/Icon/IconDelete";
import { IconButton } from "@/components/ui/IconButton";
import { InputLabel } from "@/components/ui/InputLabel";
import { Button } from "@/components/ui/NewButton";
import { TextInput } from "@/components/ui/TextInput";

import { StationSearchModal } from "./StationSearchModal";

import type { Control, FieldErrors } from "react-hook-form";
import type { TypePortalInfoForm } from "../../types";

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const InputWrapper = styled.div`
  margin-bottom: 20px;
`;

const ErrorText = styled(CommonErrorText)`
  margin-top: 4px;
`;

const StyledIconButton = styled(IconButton)`
  background-color: transparent;
`;

const StationList = styled.ul``;

const StationListItem = styled.li`
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e2e3e5;
  width: 520px;
  padding: 10px 0;
`;

const StationName = styled.p`
  font-weight: bold;
`;

const StyledButton = styled(Button)`
  width: 120px;
  height: 36px;
`;

const StyledTextInput = styled(TextInput)<{ $width: number }>`
  width: ${({ $width }) => `${$width}px`};
`;

type Props = {
  control: Control<TypePortalInfoForm>;
  errors: FieldErrors<TypePortalInfoForm>;
};

export const PortalInfoStationForm: React.FC<Props> = ({ control, errors }) => {
  const [isStationSearchOpen, setIsStationSearchOpen] = useState(false);

  return (
    <InputWrapper>
      <StyledLabel label="最寄り駅" />
      <StyledButton
        varient="ordinary"
        onClick={() => setIsStationSearchOpen(true)}
      >
        最寄り駅追加
      </StyledButton>

      <Controller
        name="hospitalStations"
        control={control}
        rules={{
          validate: (stations) => {
            if (
              stations?.some(
                (station) => `${station.walkingMinute}`.trim() === "",
              )
            ) {
              return "未入力の所要時間があります";
            }

            if (
              stations?.some(
                (station) =>
                  isNaN(Number(`${station.walkingMinute}`)) ||
                  Math.sign(Number(`${station.walkingMinute}`)) < 0,
              )
            ) {
              return "正しい数値で入力してください";
            }

            if (
              stations?.some((station) => `${station.walkingMinute}`.length > 3)
            ) {
              return "3桁以内の数値で入力してください";
            }

            return true;
          },
        }}
        render={({ field: { value, onChange } }) => (
          <>
            <StationList>
              {value?.map((station) => (
                <StationListItem key={station.stationId}>
                  <StationName>{station.stationName}駅</StationName>
                  <Flex gap={20}>
                    <Flex align="center" gap={4}>
                      徒歩
                      <StyledTextInput
                        $width={60}
                        value={station.walkingMinute?.toString()}
                        hasError={!!errors.hospitalStations}
                        onChange={(e) => {
                          onChange(
                            value.map((curr) => {
                              if (curr.stationId !== station.stationId) {
                                return curr;
                              }
                              return {
                                ...curr,
                                walkingMinute: e.target.value,
                              };
                            }),
                          );
                        }}
                      />
                      分
                    </Flex>
                    <StyledIconButton
                      varient="icon-only"
                      icon={<SvgIconDelete />}
                      onClick={() => {
                        onChange(
                          value.filter(
                            (curr) => curr.stationId !== station.stationId,
                          ),
                        );
                      }}
                    />
                  </Flex>
                </StationListItem>
              ))}
            </StationList>
            <StationSearchModal
              isOpen={isStationSearchOpen}
              onClose={() => setIsStationSearchOpen(false)}
              defaultStations={value}
              onChange={(selected) => onChange(selected)}
            />
          </>
        )}
      />
      {errors.hospitalStations && (
        <ErrorText>{errors.hospitalStations.message}</ErrorText>
      )}
    </InputWrapper>
  );
};
