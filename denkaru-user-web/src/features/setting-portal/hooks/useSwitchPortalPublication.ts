import { useForm } from "react-hook-form";

import { useUpdatePortalHospitalMutation } from "@/apis/gql/operations/__generated__/portal-hospital";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";

import type { GetPortalHospitalQuery } from "@/apis/gql/operations/__generated__/portal-hospital";

type FormType = {
  isActive: boolean;
};

export const useSwitchPortalPublication = (
  hospital: NonNullable<
    GetPortalHospitalQuery["getPortalHospitalById"]["portalHospital"]
  >,
) => {
  const { notification } = useGlobalNotification();
  const { handleError } = useErrorHandler();
  const { control, handleSubmit, watch, setValue } = useForm<FormType>({
    defaultValues: {
      isActive: hospital.isActive,
    },
  });

  const currentActiveFlag = watch("isActive");

  const [updatePortalHospital, { loading: submitting }] =
    useUpdatePortalHospitalMutation();

  const submit = (input: FormType) => {
    updatePortalHospital({
      variables: {
        input: {
          portalHospitalInput: {
            // 公開状況のみ変更
            isActive: input.isActive,

            // 更新しないパラメータ
            name: hospital.name,
            postCode: hospital.postCode,
            address1: hospital.address1,
            address2: hospital.address2,
            telephone: hospital.telephone,
            paymentDetails: hospital.paymentDetails,
            isCarpark: hospital.isCarpark,
            carparkDetail: hospital.carparkDetail,
            timelineDescription: hospital.timelineDescription,
            holidayDetail: hospital.holidayDetail,
            hospitalStations: (hospital.hospitalStations ?? []).map(
              (station) => ({
                stationId: station.stationId,
                walkingMinute: station.walkingMinute,
              }),
            ),
            tagIds: hospital.tags.map((tag) => tag.tagId),
            examinationIds: hospital.examinations.map(
              (exam) => exam.examinationId,
            ),
            specialistIds: (hospital.specialists ?? []).map(
              (specialist) => specialist.specialistId,
            ),
            descriptionTitle: hospital.descriptionTitle,
            description: hospital.description,
            mailAddress: hospital.mailAddress,
          },
        },
      },
      onCompleted: () => {
        notification.success({ message: "公開状況を更新しました" });
      },
      onError: (error) => {
        // 更新に失敗した場合、フラグを戻す
        setValue("isActive", !currentActiveFlag);
        handleError({ error, commonMessage: "公開状況の更新に失敗しました" });
      },
      refetchQueries: ["getPortalHospital"],
    });
  };

  return {
    submitting,
    onSubmit: handleSubmit(submit),
    submitHandler: submit,
    control,
    currentActiveFlag,
  };
};
