import { useCallback, useEffect, useRef, useState } from "react";

import type { RefObject } from "react";

export const usePortalPreview = (
  iframeRef: RefObject<HTMLIFrameElement | null>,
  previewKey?: number,
) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const messageChannelRef = useRef<MessageChannel | null>(null);

  useEffect(() => {
    const iframeCurrent = iframeRef.current;
    if (!iframeCurrent) {
      return;
    }

    setIsLoading(true);
    let messageChannel: MessageChannel | null = null;

    const handleIframeLoad = () => {
      // Clean up previous message channel if exists
      if (messageChannelRef.current?.port1) {
        messageChannelRef.current.port1.close();
      }

      // Create new MessageChannel
      messageChannel = new MessageChannel();
      messageChannelRef.current = messageChannel;

      const targetOrigin = process.env.NEXT_PUBLIC_BOOKING_CLIENT_URL;

      // Listen for messages from iframe
      messageChannel.port1.onmessage = (event: MessageEvent) => {
        if (event.data?.type === "READY") {
          setIsConnected(true);
          setIsLoading(false);
        }
      };

      // Send connection message with port2 to iframe
      setTimeout(() => {
        if (iframeCurrent?.contentWindow && messageChannel) {
          iframeCurrent.contentWindow.postMessage(
            { type: "CONNECT" },
            targetOrigin ?? "*",
            [messageChannel.port2],
          );
        }
      }, 100);
    };

    // Add load event listener
    iframeCurrent.addEventListener("load", handleIframeLoad);

    // If iframe is already loaded, handle immediately
    if (iframeCurrent.contentDocument?.readyState === "complete") {
      handleIframeLoad();
    }

    // Timeout fallback
    const timeoutId = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => {
      clearTimeout(timeoutId);
      if (iframeCurrent) {
        iframeCurrent.removeEventListener("load", handleIframeLoad);
      }

      // Clean up message channel
      if (messageChannel?.port1) {
        messageChannel.port1.close();
      }

      setIsConnected(false);
      setIsLoading(true);
      messageChannelRef.current = null;
    };
  }, [iframeRef, previewKey]);

  const postData = useCallback(
    (data: unknown) => {
      if (!isConnected || !messageChannelRef.current?.port1) {
        return;
      }

      try {
        messageChannelRef.current.port1.postMessage({
          type: "SET_DATA",
          payload: data,
        });
      } catch (error) {
        console.error("Error posting data to iframe:", error);
      }
    },
    [isConnected],
  );

  return { postData, isLoading, isConnected };
};
