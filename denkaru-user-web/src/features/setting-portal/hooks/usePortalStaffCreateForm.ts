import { useState } from "react";

import { ApolloError } from "@apollo/client";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";

import { useGetPortalHospitalStaffUploadFileUrlsLazyQuery } from "@/apis/gql/operations/__generated__/portal-hospital";
import {
  useCreatePortalStaffMutation,
  useEditPortalStaffMutation,
} from "@/apis/gql/operations/__generated__/portal-staff";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";
import { logger } from "@/utils/sentry-logger";

import { getPortalHospitalStaffUploadFileUrls } from "../utils";

import type { UploadFile } from "antd";
import type { CreatePortalHospitalStaffInput } from "@/apis/gql/generated/types";

type FormType = {
  name: NonNullable<
    CreatePortalHospitalStaffInput["hospitalStaffInput"]
  >["name"];
  description: NonNullable<
    CreatePortalHospitalStaffInput["hospitalStaffInput"]
  >["description"];
  specialistDetail: NonNullable<
    CreatePortalHospitalStaffInput["hospitalStaffInput"]
  >["specialistDetail"];
  isDirector: NonNullable<
    CreatePortalHospitalStaffInput["hospitalStaffInput"]
  >["isDirector"];
  files: UploadFile[];
  experienceDetail: NonNullable<
    CreatePortalHospitalStaffInput["hospitalStaffInput"]
  >["experienceDetail"];
};

export const usePortalStaffCreateForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { push } = useRouter();
  const { notification } = useGlobalNotification();
  const { handleError } = useErrorHandler();
  const [createPortalStaff] = useCreatePortalStaffMutation();
  const [editPortalStaff] = useEditPortalStaffMutation();
  const [getUploadFileUrls] =
    useGetPortalHospitalStaffUploadFileUrlsLazyQuery();
  const [isAgreementOpen, setIsAgreementOpen] = useState(false);

  const handleAgreementOpen = () => {
    setIsAgreementOpen(true);
  };

  const handleAgreementClose = () => {
    setIsAgreementOpen(false);
  };

  const {
    register,
    control,
    handleSubmit,
    formState: { errors, isDirty, isValid },
    getValues,
  } = useForm<FormType>({
    mode: "onChange",
    defaultValues: {
      name: "",
      description: "",
      specialistDetail: "",
      isDirector: false,
      files: [],
      experienceDetail: "",
    },
  });

  const submit = async (input: FormType) => {
    setIsSubmitting(true);

    try {
      const hospitalStaffInput: CreatePortalHospitalStaffInput["hospitalStaffInput"] =
        {
          name: input.name,
          description: input.description,
          specialistDetail: input.specialistDetail,
          isDirector: input.isDirector,
          experienceDetail: input.experienceDetail,
        };

      const createStaffInput: CreatePortalHospitalStaffInput = {
        filesInput: [], // 作成前時点ではstaffIdが生成されていないのでonCompleteでファイル追加のみを行う
        hospitalStaffInput,
      };

      const { data } = await createPortalStaff({
        variables: {
          input: createStaffInput,
        },
        refetchQueries: ["getPortalHospitalStaffs"],
      });

      const staffId = data?.createPortalHospitalStaff.portalStaffId;

      if (input.files.length && typeof staffId !== "undefined") {
        // ファイル追加のみの更新処理を行う（設計を見直した方が良いかも）
        const files = input.files.length
          ? await getPortalHospitalStaffUploadFileUrls(
              staffId,
              input.files,
              getUploadFileUrls,
            )
          : [];

        await editPortalStaff({
          variables: {
            input: {
              hospitalStaffId: staffId,
              addedFiles: files,
              hospitalStaffInput,
            },
          },
          onError: (error) => {
            handleError({
              error,
              commonMessage: "紹介用画像の更新に失敗しました",
            });
          },
        });
      }

      await push("/setting/portal/staff");
      notification.success({ message: "医師情報を作成しました" });
    } catch (error) {
      logger({ error, message: "failed to create portal staff" });
      if (error instanceof ApolloError || error instanceof Error) {
        handleError({
          error,
          commonMessage: "医師情報の作成に失敗しました",
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    register,
    control,
    onSubmit: handleSubmit(submit),
    errors,
    isAgreementOpen,
    handleAgreementOpen: handleSubmit(handleAgreementOpen),
    handleAgreementClose,
    isSubmitting,
    isDirty,
    getValues,
    isValid,
  };
};
