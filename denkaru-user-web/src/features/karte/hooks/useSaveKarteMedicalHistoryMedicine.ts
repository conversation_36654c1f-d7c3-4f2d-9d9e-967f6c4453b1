import { useMemo, useState } from "react";

import { notification } from "antd";
import { debounce } from "lodash";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";

import {
  usePostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation,
  usePostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation,
  useSaveKarteMedicineHistoryOtcMedicineMutation,
} from "@/apis/gql/operations/__generated__/karte-medical-history";
import { useSearchMedicalHistoryOtcMasterItem } from "@/features/karte/hooks/useSearchMedicalHistoryOtcMasterItem";
import { convertDateNumber } from "@/features/karte/utils/date";
import { useErrorHandler } from "@/hooks/useErrorHandler";

import { DRUG_MODAL_TYPE } from "../ui/SimpleKarte/MedicalHistory/SaveOtherDrugModal";

import { useSearchSupplement } from "./useSearchSupplement";
import { useSearchTenMasterItem } from "./useSearchTenMasterItem";

import type {
  PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation,
  SaveKarteMedicineHistoryOtcMedicineMutation,
} from "@/apis/gql/operations/__generated__/karte-medical-history";
import type {
  DomainModelsMstItemOtcItemModel,
  DomainModelsMstItemSearchSupplementModel,
  EmrCloudApiResponsesMstItemTenItemDto,
} from "@/apis/gql/generated/types";
import type {
  Control,
  FieldErrors,
  UseFormGetValues,
  UseFormWatch,
  UseFormSetValue,
} from "react-hook-form";

type ReturnType = {
  onSubmit: () => void;
  onDelete: () => void;
  control: Control<FormType, undefined>;
  errors: FieldErrors<FormType>;
  getValues: UseFormGetValues<FormType>;
  handleSearchMedicine: (value: string) => void;
  handleMedicineDropdownScroll: (event: React.UIEvent<HTMLDivElement>) => void;
  isLoadingMedicineList: boolean;
  medicineOptions: { value?: string; label?: string }[];
  watch: UseFormWatch<FormType>;
  setValue: UseFormSetValue<FormType>;
};

export type FormType = {
  name: string;
  itemCd?: string;
  indexCd?: string;
  startDate: number;
  endDate: number;
  cmt: string;
  isDeleted: number;
};

export const convertDataToFormType = (
  type: (typeof DRUG_MODAL_TYPE)[keyof typeof DRUG_MODAL_TYPE],
  /* eslint-disable @typescript-eslint/no-explicit-any */
  item: any,
) => {
  if (!item) {
    // Provide a default structure in case item is null
    return {
      startDate: 0,
      endDate: 0,
      cmt: "",
      isDeleted: 0,
      name: "",
      seqNo: 0,
    };
  }
  const commonData = {
    ...item,
    startDate: item.startDate || 0,
    endDate: item.endDate || 0,
    cmt: item.cmt || "",
    isDeleted: 0,
    seqNo: item.seqNo,
  };
  switch (type) {
    case DRUG_MODAL_TYPE.OTHER_DRUG:
      return {
        ...commonData,
        name: item?.drugName,
      };
    case DRUG_MODAL_TYPE.OTC:
      return {
        ...commonData,
        name: item?.tradeName,
      };
    case DRUG_MODAL_TYPE.SUPPLEMENT:
      return {
        ...commonData,
        name: item?.indexWord,
      };
    default:
      return commonData;
  }
};

const convertFormTypeToSubmit = (
  type: (typeof DRUG_MODAL_TYPE)[keyof typeof DRUG_MODAL_TYPE],
  data: FormType,
  /* eslint-disable @typescript-eslint/no-explicit-any */
  item: any,
  ptId: string,
  medicineOptions: { value?: string; label?: string }[],
) => {
  let resultData = {
    ...item,
    ...data,
    startDate: convertDateNumber({
      type: "startDate",
      date: data.startDate,
    }),
    endDate: convertDateNumber({ type: "endDate", date: data.endDate }),
    cmt: data.cmt,
    isDeleted: data.isDeleted,
    ptId,
  };

  switch (type) {
    case DRUG_MODAL_TYPE.OTHER_DRUG:
      resultData = {
        itemCd: "",
        seqNo: "0",
        sortNo: 0,
        ...resultData,
        drugName:
          medicineOptions
            .find((item) => item.value === data.name)
            ?.label?.trim() ?? data.name,
      };
      break;
    case DRUG_MODAL_TYPE.OTC:
      resultData = {
        ...resultData,
        tradeName: data.name,
      };
      break;
    case DRUG_MODAL_TYPE.SUPPLEMENT:
      resultData = {
        sortNo: 0,
        indexCd: "0",
        ...resultData,
        indexWord:
          medicineOptions
            .find((item) => item.value === data.name)
            ?.label?.trim() ?? data.name,
      };
      break;
    default:
      break;
  }

  delete resultData.name;

  return { ...resultData };
};

type UseSearchNameReturnType = {
  medicineOptions: { value?: string }[];
  handleSearchMedicine: (value: string) => void;
  handleMedicineDropdownScroll: (event: React.UIEvent<HTMLDivElement>) => void;
  currentKeyword: string;
  masterDataPageIndex: number;
  isLoadingMedicineList: boolean;
};

type UseSearchNameProps = {
  type: (typeof DRUG_MODAL_TYPE)[keyof typeof DRUG_MODAL_TYPE];
  /* eslint-disable @typescript-eslint/no-explicit-any */
  useSearch: any;
};

const useSearchName = ({
  useSearch,
  type,
}: UseSearchNameProps): UseSearchNameReturnType => {
  const [currentKeyword, setCurrentKeyword] = useState<string>("");
  const [masterDataPageIndex, setMasterDataPageIndex] = useState<number>(1);

  const {
    masterDataList,
    handleGetMasterDataList,
    isSearching: isLoadingMedicineList,
  } = useSearch();

  const medicineOptions = useMemo(() => {
    let key = "";
    let keyValue = "";
    let renderLabel = null;
    switch (type) {
      case DRUG_MODAL_TYPE.OTHER_DRUG:
        key = "name";
        keyValue = "itemCd";
        renderLabel = (item: EmrCloudApiResponsesMstItemTenItemDto) => {
          return item.name;
        };
        break;
      case DRUG_MODAL_TYPE.OTC:
        key = "tradeName";
        renderLabel = (item: DomainModelsMstItemOtcItemModel) => {
          return `${item.tradeName} ／ ${item.makerName}`;
        };
        break;
      case DRUG_MODAL_TYPE.SUPPLEMENT:
        key = "indexWord";
        keyValue = "indexCd";
        renderLabel = (item: DomainModelsMstItemSearchSupplementModel) => {
          return item.indexWord;
        };
        break;
      default:
        break;
    }

    /* eslint-disable @typescript-eslint/no-explicit-any */
    return masterDataList
      .filter((item: any) => item?.[key]?.toLowerCase())
      .map((item: any) => ({
        value: !keyValue ? item?.[key] : item?.[keyValue],
        label: renderLabel ? renderLabel(item) : item?.[key],
      }));
  }, [masterDataList, type]);

  const handleSearchMedicine = useMemo(() => {
    const search = (value: string) => {
      setCurrentKeyword(value);
      handleGetMasterDataList({
        keyword: value,
        pageIndex: 1,
      });
    };
    return debounce(search, 300);
  }, [handleGetMasterDataList]);

  const handleMedicineDropdownScroll = (
    event: React.UIEvent<HTMLDivElement>,
  ) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
    if (scrollTop + clientHeight >= scrollHeight) {
      setMasterDataPageIndex((value) => value + 1);
      handleGetMasterDataList({
        keyword: currentKeyword,
        pageIndex: masterDataPageIndex + 1,
      });
    }
  };

  return {
    medicineOptions,
    handleSearchMedicine,
    handleMedicineDropdownScroll,
    currentKeyword,
    masterDataPageIndex,
    isLoadingMedicineList,
  };
};

export const useSaveKarteMedicalHistoryMedicine = (
  type: (typeof DRUG_MODAL_TYPE)[keyof typeof DRUG_MODAL_TYPE],
  /* eslint-disable @typescript-eslint/no-explicit-any */
  item: any,
  onSuccess: () => void,
): ReturnType => {
  const {
    query: { id },
  } = useRouter();

  const { handleError } = useErrorHandler();

  const {
    handleSubmit,
    setValue,
    formState: { errors },
    control,
    watch,
    getValues,
  } = useForm<FormType>({
    defaultValues: convertDataToFormType(type, item),
  });

  const switchSearch = () => {
    let useSearch = () => {};
    switch (type) {
      case DRUG_MODAL_TYPE.OTHER_DRUG:
        useSearch = useSearchTenMasterItem;
        break;
      case DRUG_MODAL_TYPE.OTC:
        useSearch = useSearchMedicalHistoryOtcMasterItem;
        break;
      case DRUG_MODAL_TYPE.SUPPLEMENT:
        useSearch = useSearchSupplement;
        break;
      default:
        break;
    }
    return useSearch;
  };

  const [saveSupply] =
    usePostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation();

  const {
    handleSearchMedicine,
    handleMedicineDropdownScroll,
    isLoadingMedicineList,
    medicineOptions,
  } = useSearchName({
    type,
    useSearch: switchSearch(),
  });

  const [saveOtherDrug] =
    usePostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation();

  const [mutation] = useSaveKarteMedicineHistoryOtcMedicineMutation();
  const onSubmit = handleSubmit((data: FormType) => {
    const newData = {
      ...data,
      name: data.name.trim(),
    };

    const resultData = convertFormTypeToSubmit(
      type,
      newData,
      item,
      String(id),
      medicineOptions,
    );

    switch (type) {
      case DRUG_MODAL_TYPE.OTHER_DRUG:
        saveOtherDrug({
          variables: {
            ...resultData,
            ptId: String(id),
          },
          onCompleted: (
            result: PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation,
          ) => {
            if (
              result.postApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrug
                ?.message === "Success"
            ) {
              onSuccess();
            } else {
              notification.error({
                message:
                  result
                    .postApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrug
                    ?.message,
              });
            }
          },
          onError: (error) => {
            handleError({ error });
          },
        });

        break;
      case DRUG_MODAL_TYPE.OTC:
        mutation({
          variables: { ...resultData },
          onCompleted: (
            result: SaveKarteMedicineHistoryOtcMedicineMutation,
          ) => {
            if (
              result.postApiKarteMedicalHistorySaveOtcMedicine?.message ==
              "Success"
            ) {
              onSuccess();
            }
          },
          onError: (error) => {
            handleError({ error });
          },
        });
        break;
      case DRUG_MODAL_TYPE.SUPPLEMENT:
        saveSupply({
          variables: {
            ...resultData,
          },
          onCompleted: (result) => {
            if (
              result.postApiKarteMedicalHistorySaveKarteMedicalHistorySupply
                ?.message == "Success"
            ) {
              onSuccess();
            }
          },
          onError: (error) => {
            handleError({ error });
          },
        });
        break;
      default:
        break;
    }
  });

  const onDelete = () => {
    setValue("isDeleted", 1);
    onSubmit();
  };

  return {
    onSubmit,
    onDelete,
    control,
    errors,
    getValues,
    handleSearchMedicine,
    handleMedicineDropdownScroll,
    isLoadingMedicineList,
    medicineOptions,
    watch,
    setValue,
  };
};
