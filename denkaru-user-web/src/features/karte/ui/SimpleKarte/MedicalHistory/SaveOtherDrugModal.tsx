import React, { useState } from "react";

import { <PERSON>Complete, Flex, Input, Spin } from "antd";
import dayjs from "dayjs";
import { Controller } from "react-hook-form";
import styled from "styled-components";

import { Form } from "@/components/functional/Form";
import { DeleteButton } from "@/components/ui/Button/DeleteButton";
import { DatePickerSuffixClear } from "@/components/ui/DatePicker";
import { ErrorText } from "@/components/ui/ErrorText";
import { SvgIconCalendar } from "@/components/ui/Icon/IconCalendar";
import { InputLabel } from "@/components/ui/InputLabel";
import { Modal } from "@/components/ui/Modal";
import { ConfirmationModal } from "@/components/ui/Modal/ConfirmationModal";
import { Button } from "@/components/ui/NewButton";
import { TextInput } from "@/components/ui/TextInput";
import { MAX_END_DATE } from "@/features/karte/constants";
import {
  convertDataToFormType,
  useSaveKarteMedicalHistoryMedicine,
} from "@/features/karte/hooks/useSaveKarteMedicalHistoryMedicine";

import type { FormType } from "@/features/karte/hooks/useSaveKarteMedicalHistoryMedicine";

const Wrapper = styled.div`
  padding: 24px;
`;

const InputWrapper = styled.div`
  margin-bottom: 20px;
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const StyledMaxLengthLabel = styled.div`
  font-size: 14px;
  color: #6a757d !important;
  margin-bottom: 6px;
`;

const StyledInput = styled(TextInput)`
  width: 100%;
  height: 36px;
  font-size: 16px;
  color: #243544;
`;

const StyledErrorText = styled(ErrorText)`
  margin-top: 8px;
`;

const StyledTextArea = styled(Input.TextArea)`
  &.ant-input-status-error {
    border-color: #e74c3c;
  }
`;

const OverrideDatePicker = styled.div`
  .ant-picker {
    width: 152px;
  }
`;

export const DRUG_MODAL_TYPE = {
  OTC: "otc",
  OTHER_DRUG: "otherDrug",
  SUPPLEMENT: "supplement",
};

type SaveOtherDrugModalProps = {
  type: (typeof DRUG_MODAL_TYPE)[keyof typeof DRUG_MODAL_TYPE];
  /* eslint-disable @typescript-eslint/no-explicit-any */
  item: any;
  onClose: () => void;
  refetch: () => void;
  title: string;
  submitLabel: string;
  deleteTitle?: string;
  deleteSectionTitle?: string;
  deleteSectionContent?: string;
};

export const SaveOtherDrugModal: React.FC<SaveOtherDrugModalProps> = ({
  type,
  item,
  onClose,
  refetch,
  title,
  submitLabel,
  deleteTitle = "",
  deleteSectionTitle = "",
  deleteSectionContent = "",
}) => {
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);

  const onSuccess = () => {
    refetch();
    onClose();
  };

  const {
    onSubmit,
    onDelete,
    control,
    errors,
    handleSearchMedicine,
    handleMedicineDropdownScroll,
    isLoadingMedicineList,
    medicineOptions,
    watch,
    setValue,
  } = useSaveKarteMedicalHistoryMedicine(type, item, onSuccess);

  const [inputLabel, setInputLabel] = useState("");

  const _handleDelete = () => {
    onDelete();
    setIsDeleteModalOpen(false);
  };

  return (
    <>
      <Modal
        width={480}
        isOpen={true}
        title={title}
        onCancel={onClose}
        footer={[
          <Button
            key="close"
            varient="tertiary"
            shape="round"
            onClick={onClose}
          >
            キャンセル
          </Button>,
          <Button
            key="save"
            varient="primary"
            shape="round"
            htmlType="submit"
            form="save-karte-medical-history-other-drug"
          >
            {submitLabel}
          </Button>,
        ]}
      >
        <Wrapper>
          <Form id="save-karte-medical-history-other-drug" onSubmit={onSubmit}>
            <InputWrapper>
              <StyledLabel required label="薬剤名" />

              <Controller
                name="name"
                control={control}
                rules={{
                  required: "薬剤名を入力してください。",
                  validate: (value) => {
                    const valueExistsInOptions =
                      medicineOptions.length > 0 &&
                      medicineOptions.some((item) => item.value === value);

                    const name = convertDataToFormType(type, item)?.name;

                    if (
                      valueExistsInOptions ||
                      ((name?.length ?? 0) > 0 && name === value)
                    ) {
                      return true;
                    }

                    if (value.trim().length === 0) {
                      return "薬剤名を入力してください。";
                    }

                    if (value.length > 20) {
                      return "20文字以内で入力してください。";
                    }

                    return true;
                  },
                }}
                render={({ field }) => (
                  <AutoComplete
                    style={{ width: "100%" }}
                    value={inputLabel ? inputLabel : field.value}
                    onSearch={handleSearchMedicine}
                    options={medicineOptions?.map((option, index) => ({
                      ...option,
                      key: `${option?.value}_${index}`,
                      label: option?.label,
                    }))}
                    onPopupScroll={handleMedicineDropdownScroll}
                    dropdownRender={(menu) => (
                      <div>
                        {menu}
                        {isLoadingMedicineList && (
                          <div style={{ textAlign: "center", padding: 8 }}>
                            <Spin size="small" />
                          </div>
                        )}
                      </div>
                    )}
                    filterOption={false}
                    onSelect={(value, option) => {
                      if (type === DRUG_MODAL_TYPE.OTC) {
                        return;
                      }
                      const actions: Record<
                        "otherDrug" | "supplement",
                        keyof FormType
                      > = {
                        otherDrug: "itemCd",
                        supplement: "indexCd",
                      };

                      setValue(
                        actions[type as "otherDrug" | "supplement"],
                        value,
                      );
                      setInputLabel(option?.label ?? value);
                    }}
                    onChange={(value) => {
                      field.onChange(value);
                      if (type === DRUG_MODAL_TYPE.OTC) {
                        return;
                      }
                      const actions: Record<
                        "otherDrug" | "supplement",
                        keyof FormType
                      > = {
                        otherDrug: "itemCd",
                        supplement: "indexCd",
                      };

                      setValue(actions[type as "otherDrug" | "supplement"], "");
                    }}
                  >
                    <StyledInput
                      {...field}
                      value={field.value}
                      hasError={!!errors.name}
                    />
                  </AutoComplete>
                )}
              />
              {errors.name && (
                <StyledErrorText>{errors.name.message}</StyledErrorText>
              )}
            </InputWrapper>
            <InputWrapper>
              <Flex gap={20}>
                <div>
                  <StyledLabel label="開始日" />
                  <Controller
                    name="startDate"
                    control={control}
                    render={({ field }) => (
                      <OverrideDatePicker>
                        <DatePickerSuffixClear
                          format="YYYY/MM/DD"
                          placeholder="YYYY/MM/DD"
                          suffixIcon={<SvgIconCalendar />}
                          allowClear={true}
                          onChange={(date) =>
                            field.onChange(
                              date
                                ? Number(dayjs(date).format("YYYYMMDD"))
                                : null,
                            )
                          }
                          value={
                            field.value
                              ? dayjs(String(field.value), "YYYYMMDD")
                              : null
                          }
                          disabledDate={(current) =>
                            watch("endDate")
                              ? current &&
                                (current.isAfter(dayjs(), "day") ||
                                  current.isAfter(
                                    dayjs(String(watch("endDate")), "YYYYMMDD"),
                                    "day",
                                  ))
                              : current && current.isAfter(dayjs(), "day")
                          }
                        />
                      </OverrideDatePicker>
                    )}
                  />
                </div>
                <div>
                  <StyledLabel label="終了日" />
                  <Controller
                    name="endDate"
                    control={control}
                    render={({ field }) => {
                      return (
                        <OverrideDatePicker>
                          <DatePickerSuffixClear
                            format="YYYY/MM/DD"
                            placeholder="YYYY/MM/DD"
                            suffixIcon={<SvgIconCalendar />}
                            allowClear={true}
                            onChange={(date) =>
                              field.onChange(
                                date
                                  ? Number(dayjs(date).format("YYYYMMDD"))
                                  : null,
                              )
                            }
                            value={
                              field.value && field.value !== MAX_END_DATE
                                ? dayjs(String(field.value), "YYYYMMDD")
                                : null
                            }
                            disabledDate={(current) =>
                              watch("startDate")
                                ? current &&
                                  (current.isAfter(dayjs(), "day") ||
                                    current.isBefore(
                                      dayjs(
                                        String(watch("startDate")),
                                        "YYYYMMDD",
                                      ),
                                      "day",
                                    ))
                                : current && current.isAfter(dayjs(), "day")
                            }
                          />
                        </OverrideDatePicker>
                      );
                    }}
                  />
                </div>
              </Flex>
            </InputWrapper>
            <InputWrapper>
              <Flex justify="space-between">
                <StyledLabel label="コメント" />
                <StyledMaxLengthLabel>
                  {watch("cmt")?.length}/100
                </StyledMaxLengthLabel>
              </Flex>
              <Controller
                control={control}
                name="cmt"
                rules={{
                  maxLength: {
                    value: 100,
                    message: "100文字以内で入力してください。",
                  },
                }}
                render={({ field }) => (
                  <>
                    <StyledTextArea
                      {...field}
                      status={errors.cmt ? "error" : ""}
                      autoSize={{ minRows: 5, maxRows: 10 }}
                    />
                    {errors.cmt && (
                      <StyledErrorText>{errors.cmt.message}</StyledErrorText>
                    )}
                  </>
                )}
              />
            </InputWrapper>
            {item && (
              <Flex justify="flex-end">
                <DeleteButton onClick={() => setIsDeleteModalOpen(true)}>
                  {deleteSectionTitle}
                </DeleteButton>
              </Flex>
            )}
          </Form>
        </Wrapper>
      </Modal>
      <ConfirmationModal
        title={deleteTitle}
        sectionTitle={deleteSectionTitle}
        sectionContent={deleteSectionContent}
        isOpen={isDeleteModalOpen}
        setIsOpen={setIsDeleteModalOpen}
        submit={_handleDelete}
      />
    </>
  );
};
