import styled from "styled-components";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: () => void;
};

const WrapperModalError = styled.div`
  padding: 14px 24px;
  font-size: 24px;
  font-weight: bold;
  color: #243544;
  height: 256px;
`;

const ErrorText = styled.div`
  font-size: 16px;
  width: 100%;
`;

export const ConfirmEditBoardModal = ({ isOpen, onClose, onSubmit }: Props) => {
  return (
    <Modal
      isOpen={isOpen}
      width={480}
      title="院内掲示板"
      footer={[
        <Button key="cancel" varient="tertiary" onClick={onClose}>
          いいえ
        </Button>,
        <Button key="submit" varient="primary" onClick={onSubmit}>
          はい
        </Button>,
      ]}
    >
      <WrapperModalError>
        <ErrorText>
          他ユーザーにより内容が更新されています。現在の編集内容で上書き保存しますか？
        </ErrorText>
      </WrapperModalError>
    </Modal>
  );
};
