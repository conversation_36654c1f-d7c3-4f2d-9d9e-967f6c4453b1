import React from "react";

import styled from "styled-components";

import { useScheduleView } from "@/features/start/hooks/useScheduleView";

import { ScheduleSection } from "../schedule-section";

import { ReserveSummarySection } from "./ReserveSummarySection";
import { HospitalBoardSection } from "./HospitalBoardSection";
import { NotifySection } from "./NotifySection";
import { TaskSection } from "./TaskSection";

const RightContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding-bottom: 20px;
`;

const LeftContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 50%;
`;

const RightContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 50%;
`;

const ContentWrapper = styled.div`
  display: flex;
  gap: 20px;
`;

export const RightContent = () => {
  const {
    currentView,
    currentDate,
    setCurrentView,
    navigateDate,
    handleDateChange,
  } = useScheduleView();

  return (
    <RightContentContainer>
      <ContentWrapper>
        <LeftContainer>
          <ReserveSummarySection />
          <ScheduleSection
            currentView={currentView}
            currentDate={currentDate}
            setCurrentView={setCurrentView}
            navigateDate={navigateDate}
            handleDateChange={handleDateChange}
          />
        </LeftContainer>
        <RightContainer>
          <HospitalBoardSection currentDate={currentDate} />
          <NotifySection />
        </RightContainer>
      </ContentWrapper>
      <TaskSection />
    </RightContentContainer>
  );
};
