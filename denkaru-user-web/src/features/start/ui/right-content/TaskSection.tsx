/* eslint-disable import/no-restricted-paths */
import React, { useMemo } from "react";

import styled from "styled-components";
import Link from "next/link";
import { useRouter } from "next/router";

import { TaskTable } from "@/features/task/ui/list/TaskListTable";
import { TaskSortType } from "@/apis/gql/generated/types";
import { useTaskListTemplate } from "@/features/task/hooks/useTaskListTemplate";
import { TASK_HOME_PAGE_SIZE } from "@/features/task/constant";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGetTaskStatusesWithTaskCountQuery } from "@/apis/gql/operations/__generated__/task";
import { TASK_COMPLETED_STATUS_NAME } from "@/constants/task";

const TaskSectionContainer = styled.div`
  min-height: 400px;
  border: 1px solid #e2e3e5;
  border-radius: 12px;
  background-color: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const Title = styled.span`
  font-size: 16px;
  color: #243544;
  font-weight: bold;
  cursor: default;
`;

const ViewAllLink = styled(Link)`
  color: #007aff;
  font-size: 14px;
  text-decoration: none;
  font-family: "NotoSansJP";
`;

const TaskTableWrapper = styled.div`
  height: 100%;
`;

export const TaskSection = () => {
  const { isReady } = useRouter();
  const { handleError } = useErrorHandler();

  const { data: taskStatusData } = useGetTaskStatusesWithTaskCountQuery({
    skip: !isReady,
    variables: {
      input: {
        responsibleStaffId: undefined,
        taskCategoryId: undefined,
      },
    },
    onError: (error) => {
      handleError({ error });
    },
  });

  const taskStatusExcludeCompleted = useMemo(
    () =>
      taskStatusData?.getTaskStatusesWithTaskCount?.taskStatuses
        .filter(
          (taskStatus) => taskStatus.statusName !== TASK_COMPLETED_STATUS_NAME,
        )
        ?.map((taskStatus) => taskStatus.statusId) || [],
    [taskStatusData],
  );

  const { taskList, isLoadingTasks } = useTaskListTemplate(
    TASK_HOME_PAGE_SIZE,
    taskStatusExcludeCompleted,
  );

  return (
    <TaskSectionContainer>
      <Header>
        <Title>タスク</Title>
        <ViewAllLink href="/task">すべて見る</ViewAllLink>
      </Header>
      <TaskTableWrapper>
        <TaskTable
          tasks={taskList}
          loading={isLoadingTasks}
          isLoadingMore={false}
          sortOrder={undefined}
          sortType={TaskSortType.CreatedDate}
          onSortTasks={() => {}}
          onLoadMore={() => {}}
        />
      </TaskTableWrapper>
    </TaskSectionContainer>
  );
};
