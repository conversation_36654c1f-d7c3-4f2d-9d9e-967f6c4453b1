import React, { useMemo } from "react";

import styled from "styled-components";
import dayjs from "dayjs";
import { useRouter } from "next/router";
import Link from "next/link";

import { useGetReservations } from "@/hooks/useGetReservations";
import { ReservationType } from "@/constants/reservation";
import { useGetTaskStatusesWithTaskCountQuery } from "@/apis/gql/operations/__generated__/task";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { TASK_COMPLETED_STATUS_NAME } from "@/constants/task";

import { formatText } from "../../utils";

const ReserveSummarySectionContainer = styled.div`
  height: 160px;
  border-radius: 12px;
  background-color: #fff;
  display: flex;
  overflow: hidden;
`;

const DateSection = styled.div`
  background-color: #005bac;
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 26px 42px;
  min-width: 200px;
`;

const YearText = styled.div`
  font-size: 22px;
  cursor: default;

  .number {
    font-family: Roboto;
  }
`;

const DateText = styled.div`
  line-height: 1.11;
  font-size: 32px;
  cursor: default;

  .number {
    font-family: Roboto;
    font-size: 54px;
  }
`;

const DayText = styled.div`
  font-size: 20px;
  cursor: default;
`;

const StatsSection = styled.div`
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
`;

const StatsGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;

  &:nth-child(2) {
    border-top: 1px solid #e2e3e5;
    padding-top: 12px;
  }
`;

const StatsTitle = styled.div`
  font-size: 16px;
  font-weight: bold;
  color: #243544;
  line-height: 1;
  cursor: default;
`;

const StatsRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
`;

const StatsLabel = styled.div`
  font-size: 16px;
  color: #243544;
  padding-left: 20px;
  line-height: 1;
  cursor: default;
`;

const StatsValue = styled(Link)`
  cursor: pointer;
  color: #007aff;
  line-height: 1;
  .number {
    font-family: Roboto;
  }
`;

export const ReserveSummarySection = () => {
  const { isReady } = useRouter();
  const { handleError } = useErrorHandler();
  const { reservations } = useGetReservations(
    {
      fromDate: dayjs().startOf("day").toISOString(),
      toDate: dayjs().endOf("day").toISOString(),
      isLoadCancelReserve: false,
      isLoadRaiinInfo: true,
    },
    !isReady,
  );

  const { data: taskStatusData } = useGetTaskStatusesWithTaskCountQuery({
    skip: !isReady,
    variables: {
      input: {
        responsibleStaffId: undefined,
        taskCategoryId: undefined,
      },
    },
    onError: (error) => {
      handleError({ error });
    },
  });

  const totalReservations = reservations?.length || 0;
  const totalInPerson =
    reservations?.filter(
      (reservation) => reservation.reserveType === ReservationType.IN_PERSON,
    ).length || 0;
  const totalOnLine =
    reservations?.filter(
      (reservation) => reservation.reserveType === ReservationType.ONLINE,
    ).length || 0;

  const totalIncompleteTasks = useMemo(() => {
    return (
      (taskStatusData?.getTaskStatusesWithTaskCount?.totalTaskCount || 0) -
      (taskStatusData?.getTaskStatusesWithTaskCount?.taskStatuses.find(
        (taskStatus) => taskStatus.statusName === TASK_COMPLETED_STATUS_NAME,
      )?.taskCount || 0)
    );
  }, [taskStatusData]);

  const currentDate = dayjs();
  const year = currentDate.format("YYYY年");
  const monthDay = currentDate.format("M月D日");
  const dayOfWeek = currentDate.format("dddd");

  return (
    <ReserveSummarySectionContainer>
      <DateSection>
        <YearText>{formatText(year)}</YearText>
        <DateText>{formatText(monthDay)}</DateText>
        <DayText>{dayOfWeek}</DayText>
      </DateSection>
      <StatsSection>
        <StatsGroup>
          <StatsRow>
            <StatsTitle>予約</StatsTitle>
            <StatsValue href={`/calendar`}>
              {formatText(`全${totalReservations}件`)}
            </StatsValue>
          </StatsRow>
          <StatsRow>
            <StatsLabel>対面</StatsLabel>
            <StatsValue href={`/calendar`}>
              {formatText(`${totalInPerson}件`)}
            </StatsValue>
          </StatsRow>
          <StatsRow>
            <StatsLabel>オンライン</StatsLabel>
            <StatsValue href={`/calendar`}>
              {formatText(`${totalOnLine}件`)}
            </StatsValue>
          </StatsRow>
        </StatsGroup>
        <StatsGroup>
          <StatsRow>
            <StatsTitle>未完了タスク</StatsTitle>
            <StatsValue href={`/task`}>
              {formatText(`${totalIncompleteTasks}件`)}
            </StatsValue>
          </StatsRow>
        </StatsGroup>
      </StatsSection>
    </ReserveSummarySectionContainer>
  );
};
