import React, { useEffect, useState } from "react";

import styled from "styled-components";
import { useF<PERSON>, Controller } from "react-hook-form";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { TextAreaInput } from "@/components/ui/TextAreaInput";
import { useEditWhiteBoardMutation } from "@/apis/gql/operations/__generated__/inhouse-notice-board";

import { ConfirmEditBoardModal } from "./ConfirmEditBoardModal";

const ModalWrapper = styled.div`
  padding: 24px;
`;

const StyledTextArea = styled(TextAreaInput)`
  width: 100%;
  border-radius: 6px;
  border: solid 1px #e2e3e5;
  background-color: #fbfcfe;
  padding: 8px;
`;

const StyledButton = styled(Button)<{ $isCancel?: boolean }>`
  width: 120px;
  height: 40px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  font-family: "NotoSansJP", sans-serif;

  ${({ $isCancel }) =>
    $isCancel &&
    `
    background-color: #e2e3e5 !important;
    color: #6a757d !important;
    border: none !important;

    &:hover {
      background-color: #d1d3d5 !important;
      opacity: 1 !important;
    }
  `}
`;

type Props = {
  isOpen: boolean;
  initialContent: string;
  onClose: () => void;
  refetch: () => void;
};

export const HospitalBoardEditModal: React.FC<Props> = ({
  isOpen,
  initialContent,
  onClose,
  refetch,
}) => {
  const [showConfirm, setShowConfirm] = useState(false);
  const [editWhiteBoard, { loading }] = useEditWhiteBoardMutation({
    onCompleted: () => {
      refetch();
    },
  });

  const {
    control,
    reset,
    watch,
    formState: { isDirty },
  } = useForm<{ content: string }>({
    defaultValues: { content: initialContent },
  });

  useEffect(() => {
    if (isOpen) {
      reset({ content: initialContent });
      setShowConfirm(false);
    }
  }, [isOpen, initialContent, reset]);

  const content = watch("content");

  const handleSave = () => {
    if (isDirty && !!initialContent?.trim().length) {
      onClose();
      setShowConfirm(true);
    } else {
      editWhiteBoard({
        variables: { contentAfter: content, contentBefore: initialContent },
      });
      onClose();
    }
  };

  const handleCancel = () => {
    onClose();
  };

  const handleConfirmSave = () => {
    editWhiteBoard({
      variables: { contentAfter: content, contentBefore: initialContent },
    });
    setShowConfirm(false);
    onClose();
  };

  const handleCloseConfirm = () => {
    setShowConfirm(false);
  };

  return (
    <>
      <Modal
        title="院内掲示板"
        isOpen={isOpen}
        onCancel={handleCancel}
        width={760}
        centered
        footer={[
          <StyledButton
            key="cancel"
            varient="tertiary"
            $isCancel={true}
            onClick={handleCancel}
          >
            キャンセル
          </StyledButton>,
          <StyledButton
            key="save"
            varient="primary"
            onClick={handleSave}
            disabled={loading}
          >
            保存
          </StyledButton>,
        ]}
      >
        <ModalWrapper>
          <Controller
            name="content"
            control={control}
            render={({ field }) => (
              <StyledTextArea
                {...field}
                placeholder="院内掲示板の内容を入力してください"
                rows={12}
              />
            )}
          />
        </ModalWrapper>
      </Modal>
      <ConfirmEditBoardModal
        isOpen={showConfirm}
        onClose={handleCloseConfirm}
        onSubmit={handleConfirmSave}
      />
    </>
  );
};
