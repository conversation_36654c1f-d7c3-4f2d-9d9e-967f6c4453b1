import { Layout } from "antd";
import styled from "styled-components";

import { StartPageSidebar } from "./left-content";
import { RightContent } from "./right-content";

const HomePageContentContainer = styled(Layout.Content)`
  display: grid;
  grid-template-columns: 380px 1fr;
  gap: 20px;
  padding: 20px 20px 0px 0px;
`;

export const HomePageContent: React.FC = () => {
  return (
    <HomePageContentContainer>
      <StartPageSidebar />
      <RightContent />
    </HomePageContentContainer>
  );
};
