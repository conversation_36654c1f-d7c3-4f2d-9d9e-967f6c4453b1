import React from "react";

import styled from "styled-components";

import { Loading } from "@/components/ui/Loading";

import { useGetScheduleList } from "../../hooks/useGetScheduleList";
import { getDateRange } from "../../utils/schedule";

import { ScheduleHeader } from "./components/ScheduleHeader";
import { BigCalendarView } from "./components/BigCalendarView";

import type { ViewType } from "../../types";
import type { Dayjs } from "dayjs";

const ScheduleSectionContainer = styled.div<{ $currentView: ViewType }>`
  max-height: ${({ $currentView }) =>
    $currentView === "day" ? "490px" : "none"};
  border-radius: 12px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  padding: 20px;
`;

interface ScheduleSectionProps {
  currentView: ViewType;
  currentDate: Dayjs;
  setCurrentView: (view: ViewType) => void;
  navigateDate: (direction: "prev" | "next") => void;
  handleDateChange: (date: Date) => void;
}

export const ScheduleSection = ({
  currentView,
  currentDate,
  setCurrentView,
  navigateDate,
  handleDateChange,
}: ScheduleSectionProps) => {
  const { startDate, endDate } = getDateRange(currentView, currentDate);

  const { schedules, loading, refetch } = useGetScheduleList(
    {
      startDate,
      endDate,
    },
    false,
  );

  if (loading) {
    return <Loading isLoading={loading} />;
  }

  return (
    <ScheduleSectionContainer $currentView={currentView}>
      <ScheduleHeader
        currentView={currentView}
        currentDate={currentDate}
        onViewChange={setCurrentView}
        onNavigateDate={navigateDate}
      />
      <BigCalendarView
        currentDate={currentDate}
        currentView={currentView}
        onNavigate={handleDateChange}
        onView={setCurrentView}
        events={schedules}
        refetchScheduleList={refetch}
      />
    </ScheduleSectionContainer>
  );
};
