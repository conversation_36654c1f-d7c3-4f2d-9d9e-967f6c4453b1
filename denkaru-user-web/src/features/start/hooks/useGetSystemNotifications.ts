import { useQuery } from "@apollo/client";

import {
  GET_READ_SYSTEM_NOTIFICATIONS,
  GET_SYSTEM_NOTIFICATIONS,
} from "@/apis/gql/operations/system-notification";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";

import type { SystemNotification } from "@/apis/gql/generated/types";

export const useGetSystemNotifications = () => {
  const { handleError } = useErrorHandler();
  const { refetchReadSystemNotifications: refetchHeader } = usePatientContext();
  const { data } = useQuery<{
    getSystemNotifications: SystemNotification[];
  }>(GET_SYSTEM_NOTIFICATIONS, {
    onError: (error) => {
      handleError({ error });
    },
    fetchPolicy: "no-cache",
  });

  const { data: readSystemNotifications, refetch: refetchRead } = useQuery<{
    getReadSystemNotifications: number[];
  }>(GET_READ_SYSTEM_NOTIFICATIONS, {
    onError: (error) => {
      handleError({ error });
    },
    fetchPolicy: "no-cache",
  });

  const refetch = () => {
    refetchRead();
    if (refetchHeader) {
      refetchHeader();
    }
  };

  return {
    systemNotifications: data?.getSystemNotifications,
    readSystemNotifications:
      readSystemNotifications?.getReadSystemNotifications,
    refetch,
  };
};
