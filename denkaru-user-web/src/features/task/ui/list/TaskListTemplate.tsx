import React, { useEffect, useState } from "react";

import styled from "styled-components";

import { Button } from "@/components/ui/NewButton";
import { TaskFilter } from "@/features/task/ui/list/TaskFilter";
import { TaskTable } from "@/features/task/ui/list/TaskListTable";

import { useTaskListTemplate } from "../../hooks/useTaskListTemplate";
import { TASK_PAGE_SIZE } from "../../constant";

import type { SortOrder, TaskSortType } from "@/apis/gql/generated/types";

const Container = styled.div`
  height: 100%;
  flex: 1;
  padding: 20px 40px;
  > * {
    margin-bottom: 15px;
  }
`;

const HeaderWrapper = styled.span`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const FlexWrapper = styled.div`
  display: flex;
  gap: 12px;
`;

const Title = styled.p`
  line-height: 20px;
  font-size: 20px;
  font-weight: bold;
`;

const SettingButton = styled(Button)`
  width: 80px;
`;

const TaskTableContainer = styled.div<{ height: number }>`
  height: ${({ height }) => `${height}px`};
`;

export const TaskListTemplate = () => {
  const [tableHeight, setTableHeight] = useState(0);

  useEffect(() => {
    setTableHeight(window.innerHeight - 460);
  }, []);

  const {
    query,
    staffsWithTaskCount,
    taskList,
    taskCategoriesWithCount,
    isLoadingTasks,
    isLoadingCategories,
    isLoadingStaffs,
    totalCountByCategory,
    isFetchingMore,
    taskStatusesWithTaskCount,
    totalTaskCountByStatus,
    handleFilterByStatus,
    handleSortChange,
    handleFilterByCategory,
    handleFilterByResponsibleStaff,
    handleTaskSettingRoute,
    handleTaskCreateRoute,
    handleLoadMoreTasks,
  } = useTaskListTemplate(TASK_PAGE_SIZE);
  return (
    <Container>
      <HeaderWrapper>
        <Title>タスク一覧</Title>
        <FlexWrapper>
          <SettingButton varient="standard" onClick={handleTaskSettingRoute}>
            設定
          </SettingButton>
          <Button varient="secondary" onClick={handleTaskCreateRoute}>
            新規タスク
          </Button>
        </FlexWrapper>
      </HeaderWrapper>

      {staffsWithTaskCount && (
        <TaskFilter
          isLoading={isLoadingStaffs}
          value={Number(query.staff) || 0}
          total={staffsWithTaskCount.totalTaskCount}
          title="担当者"
          handleFilterChange={handleFilterByResponsibleStaff}
          options={staffsWithTaskCount.staffs.map((staff) => ({
            value: staff.staffId,
            name: staff.staffName,
            count: staff.taskCount,
          }))}
        />
      )}

      {taskCategoriesWithCount && (
        <TaskFilter
          isLoading={isLoadingCategories}
          title="カテゴリー"
          total={totalCountByCategory}
          value={Number(query.category) || 0}
          handleFilterChange={handleFilterByCategory}
          options={taskCategoriesWithCount.map((category) => ({
            value: category.categoryId,
            name: category.categoryName,
            count: category.taskCount,
          }))}
        />
      )}

      {taskStatusesWithTaskCount && (
        <TaskFilter
          title="ステータス"
          total={totalTaskCountByStatus}
          value={Number(query.status) || 0}
          handleFilterChange={handleFilterByStatus}
          options={taskStatusesWithTaskCount.map((status) => ({
            value: status.statusId,
            name: status.statusName,
            count: status.taskCount,
          }))}
        />
      )}
      <TaskTableContainer height={tableHeight}>
        <TaskTable
          isLoadingMore={isFetchingMore}
          onLoadMore={handleLoadMoreTasks}
          tasks={taskList}
          loading={isLoadingTasks}
          onSortTasks={handleSortChange}
          sortOrder={query.order as SortOrder}
          sortType={query.sort as TaskSortType}
        />
      </TaskTableContainer>
    </Container>
  );
};
