import { useMemo, useState } from "react";

import { useSearchParams } from "next/navigation";
import { useRouter } from "next/router";

import { SortOrder, TaskSortType } from "@/apis/gql/generated/types";
import { useGetStaffListWithTaskCountQuery } from "@/apis/gql/operations/__generated__/staff";
import {
  useGetTaskCategoryWithCountQuery,
  useGetTasksQuery,
  useGetTaskStatusesWithTaskCountQuery,
} from "@/apis/gql/operations/__generated__/task";
import { useErrorHandler } from "@/hooks/useErrorHandler";

import { TASK_PAGE_SIZE } from "../constant";

import type { GetTasksInput } from "@/apis/gql/generated/types";

export const useTaskListTemplate = (
  pageSize: number,
  taskStatusExcludeCompleted?: number[],
) => {
  const { query, pathname, push, replace, isReady } = useRouter();
  const { handleError } = useErrorHandler();
  const searchParams = useSearchParams();
  const [hasMore, setHasMore] = useState(true);
  const [isFetchingMore, setIsFetchingMore] = useState(false);

  const { data: staffData, loading: isLoadingStaffs } =
    useGetStaffListWithTaskCountQuery({
      onError: (error) => {
        handleError({ error });
      },
    });

  const { loading: isLoadingCategories, data: getTaskCategoryData } =
    useGetTaskCategoryWithCountQuery({
      skip: !isReady,
      variables: {
        input: { responsibleStaffId: Number(query.staff) || undefined },
      },
      onError: (error) => {
        handleError({ error });
      },
    });

  const { data: taskStatusData } = useGetTaskStatusesWithTaskCountQuery({
    skip: !isReady,
    variables: {
      input: {
        responsibleStaffId: Number(query.staff),
        taskCategoryId: Number(query.category),
      },
    },
    onError: (error) => {
      handleError({ error });
    },
  });

  const getTasksInput = useMemo(
    () => ({
      limit: pageSize,
      categoryId: Number(query.category) || undefined,
      responsibleStaffId: Number(query.staff) || undefined,
      order:
        query.order === SortOrder.Ascend || query.order === SortOrder.Descend
          ? query.order
          : undefined,
      sort:
        query.sort === TaskSortType.CreatedDate ||
        query.sort === TaskSortType.ExpiredAt
          ? query.sort
          : undefined,
      statusIds: query.status
        ? [Number(query.status)]
        : taskStatusExcludeCompleted,
    }),
    [
      query.category,
      query.order,
      query.sort,
      query.staff,
      query.status,
      pageSize,
      taskStatusExcludeCompleted,
    ],
  );

  const {
    loading: isLoadingTasks,
    data: getTasksData,
    fetchMore,
  } = useGetTasksQuery({
    skip: !isReady,
    variables: {
      input: {
        ...getTasksInput,
      },
    },
    onError: (error) => {
      handleError({ error });
    },
    onCompleted: (data) => {
      if (data.getTasks?.tasks && data.getTasks.tasks.length < TASK_PAGE_SIZE) {
        setHasMore(false);
      }
    },
  });

  const handleLoadMoreTasks = (cursorId: string, cursorDate?: string) => {
    if (!hasMore) return;
    setIsFetchingMore(true);
    fetchMore({
      variables: {
        input: {
          ...getTasksInput,
          cursorId,
          cursorDate,
        },
      },
      updateQuery: (previousResult, { fetchMoreResult }) => {
        setIsFetchingMore(false);
        const currentTasks = previousResult.getTasks?.tasks || [];
        const newTasks = fetchMoreResult.getTasks?.tasks || [];

        if (newTasks.length < TASK_PAGE_SIZE) {
          setHasMore(false);
        }

        return {
          getTasks: {
            ...fetchMoreResult.getTasks,
            tasks: [...currentTasks, ...newTasks],
          },
        };
      },
    });
  };

  const updateTaskParams = ({
    categoryId,
    responsibleStaffId,
    statusId,
    sort,
    order,
  }: Partial<GetTasksInput & { statusId: number }>) => {
    const params = new URLSearchParams(searchParams);

    const handleParam = (key: string, value: string | number | undefined) => {
      if (value !== undefined) {
        if (value) {
          params.set(key, value.toString());
        } else {
          params.delete(key);
        }
      }
    };

    handleParam("category", categoryId);
    handleParam("staff", responsibleStaffId);
    handleParam("status", statusId);
    handleParam("sort", sort);
    handleParam("order", order);

    replace(`${pathname}?${params.toString()}`);
  };

  const handleFilterByCategory = (value: number) => {
    setHasMore(true);
    updateTaskParams({ categoryId: value });
  };

  const handleFilterByResponsibleStaff = (value: number) => {
    setHasMore(true);
    updateTaskParams({ responsibleStaffId: value });
  };

  const handleFilterByStatus = (value: number) => {
    setHasMore(true);
    updateTaskParams({ statusId: value });
  };

  const handleTaskSettingRoute = () => {
    push("/task/setting");
  };

  const handleTaskCreateRoute = () => {
    push("/task/create");
  };

  const handleSortChange = (sortField: string, sortOrder: SortOrder | null) => {
    if (sortOrder === null) return;
    setHasMore(true);
    updateTaskParams({ sort: sortField as TaskSortType, order: sortOrder });
  };

  return {
    query,
    staffsWithTaskCount: staffData?.getStaffListWithTaskCount,
    taskList: getTasksData?.getTasks?.tasks,
    taskCategoriesWithCount:
      getTaskCategoryData?.getTaskCategoryWithCount?.taskCategories,
    isLoadingTasks,
    isLoadingStaffs,
    isLoadingCategories,
    totalCountByCategory:
      getTaskCategoryData?.getTaskCategoryWithCount?.totalCount,
    handleSortChange,
    handleFilterByCategory,
    handleFilterByResponsibleStaff,
    handleTaskSettingRoute,
    handleTaskCreateRoute,
    handleLoadMoreTasks,
    isFetchingMore,
    taskStatusesWithTaskCount:
      taskStatusData?.getTaskStatusesWithTaskCount?.taskStatuses,
    totalTaskCountByStatus:
      taskStatusData?.getTaskStatusesWithTaskCount?.totalTaskCount,
    handleFilterByStatus,
  };
};
