import { WithClinicAuth } from "@/components/functional/WithClinicAuth";
import { NoPermission } from "@/components/ui/NoPermission";
import { PortalNewsEdit } from "@/features/setting-portal/ui/news/PortalNewsEdit";
import { useGetUserPermissions } from "@/hooks/useGetUserPermissions";
import { ClinicMapContent } from "@/features/clinic-map/ui/ClinicMapContent";

const NotificationEditPage = () => {
  const { isSessionLoading, pages } = useGetUserPermissions();

  if (isSessionLoading) return null;

  // クエリを実行させないためルートファイルで判定
  if (!pages.settingPortalEnabled) {
    return (
      <ClinicMapContent>
        <NoPermission />
      </ClinicMapContent>
    );
  }

  return (
    <ClinicMapContent>
      <PortalNewsEdit />
    </ClinicMapContent>
  );
};

export default WithClinicAuth(NotificationEditPage);
