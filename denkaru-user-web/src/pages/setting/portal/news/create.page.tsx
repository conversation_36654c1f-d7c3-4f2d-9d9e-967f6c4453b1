import { WithClinicAuth } from "@/components/functional/WithClinicAuth";
import { NoPermission } from "@/components/ui/NoPermission";
import { PortalNewsCreate } from "@/features/setting-portal/ui/news/PortalNewsCreate";
import { useGetUserPermissions } from "@/hooks/useGetUserPermissions";
import { ClinicMapContent } from "@/features/clinic-map/ui/ClinicMapContent";

const NotificationCreatePage = () => {
  const { isSessionLoading, pages } = useGetUserPermissions();

  if (isSessionLoading) return null;

  // クエリを実行させないためルートファイルで判定
  if (!pages.settingPortalEnabled) {
    return (
      <ClinicMapContent>
        <NoPermission />
      </ClinicMapContent>
    );
  }

  return (
    <ClinicMapContent>
      <PortalNewsCreate />
    </ClinicMapContent>
  );
};

export default WithClinicAuth(NotificationCreatePage);
