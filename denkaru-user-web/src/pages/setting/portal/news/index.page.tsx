import { WithClinicAuth } from "@/components/functional/WithClinicAuth";
import { NoPermission } from "@/components/ui/NoPermission";
import { ClinicMapContent } from "@/features/clinic-map/ui/ClinicMapContent";
import { PortalNews } from "@/features/setting-portal/ui/news/PortalNews";
import { useGetUserPermissions } from "@/hooks/useGetUserPermissions";

const PortalNewsPage = () => {
  const { isSessionLoading, pages } = useGetUserPermissions();

  if (isSessionLoading) return null;

  // クエリを実行させないためルートファイルで判定
  if (!pages.settingPortalEnabled) {
    return (
      <ClinicMapContent>
        <NoPermission />
      </ClinicMapContent>
    );
  }

  return (
    <ClinicMapContent>
      <PortalNews />
    </ClinicMapContent>
  );
};

export default WithClinicAuth(PortalNewsPage);
