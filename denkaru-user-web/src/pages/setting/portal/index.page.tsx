import { type RefObject } from "react";

import { WithClinicAuth } from "@/components/functional/WithClinicAuth";
import { NoPermission } from "@/components/ui/NoPermission";
import { ClinicMapContent } from "@/features/clinic-map/ui/ClinicMapContent";
import { PortalInfo } from "@/features/setting-portal/ui/info/PortalInfo";
import { useGetUserPermissions } from "@/hooks/useGetUserPermissions";
import { usePortal } from "@/providers/PortalProvider";

type PortalInfoPageContentProps = {
  iframeRef?: RefObject<HTMLIFrameElement | null>;
  onTogglePreview?: () => void;
  previewKey?: number;
};

const PortalInfoPageContent = ({
  iframeRef,
  onTogglePreview,
  previewKey,
}: PortalInfoPageContentProps) => {
  const { hospital, tags, examination, specialist } = usePortal();

  if (!tags || !examination || !specialist) {
    return null;
  }

  return (
    <PortalInfo
      hospital={hospital}
      tags={tags}
      examination={examination}
      specialist={specialist}
      iframeRef={iframeRef}
      onTogglePreview={onTogglePreview}
      previewKey={previewKey}
    />
  );
};

const PortalInfoPage = () => {
  const { isSessionLoading, pages } = useGetUserPermissions();
  if (isSessionLoading) return null;

  if (!pages.settingPortalEnabled) {
    return (
      <ClinicMapContent>
        <NoPermission />
      </ClinicMapContent>
    );
  }

  return (
    <ClinicMapContent>
      <PortalInfoPageContent />
    </ClinicMapContent>
  );
};

export default WithClinicAuth(PortalInfoPage);
