import { WithClinicAuth } from "@/components/functional/WithClinicAuth";
import { NoPermission } from "@/components/ui/NoPermission";
import { ClinicMapContent } from "@/features/clinic-map/ui/ClinicMapContent";
import { PortalStaff } from "@/features/setting-portal/ui/staff/PortalStaff";
import { useGetUserPermissions } from "@/hooks/useGetUserPermissions";

const PortalStaffPage = () => {
  const { isSessionLoading, pages } = useGetUserPermissions();

  if (isSessionLoading) return null;

  // クエリを実行させないためルートファイルで判定
  if (!pages.settingPortalEnabled) {
    return (
      <ClinicMapContent>
        <NoPermission />
      </ClinicMapContent>
    );
  }

  return (
    <ClinicMapContent>
      <PortalStaff />
    </ClinicMapContent>
  );
};

export default WithClinicAuth(PortalStaffPage);
