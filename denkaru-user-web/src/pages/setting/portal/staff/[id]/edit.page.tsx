import { WithClinicAuth } from "@/components/functional/WithClinicAuth";
import { NoPermission } from "@/components/ui/NoPermission";
import { PortalStaffEdit } from "@/features/setting-portal/ui/staff/PortalStaffEdit";
import { useGetUserPermissions } from "@/hooks/useGetUserPermissions";
import { ClinicMapContent } from "@/features/clinic-map/ui/ClinicMapContent";

const StaffEditPage = () => {
  const { isSessionLoading, pages } = useGetUserPermissions();

  if (isSessionLoading) return null;

  // クエリを実行させないためルートファイルで判定
  if (!pages.settingPortalEnabled) {
    return (
      <ClinicMapContent>
        <NoPermission />
      </ClinicMapContent>
    );
  }

  return (
    <ClinicMapContent>
      <PortalStaffEdit />
    </ClinicMapContent>
  );
};

export default WithClinicAuth(StaffEditPage);
