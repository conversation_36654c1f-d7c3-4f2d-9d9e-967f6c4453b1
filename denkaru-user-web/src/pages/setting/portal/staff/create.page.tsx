import { WithClinicAuth } from "@/components/functional/WithClinicAuth";
import { NoPermission } from "@/components/ui/NoPermission";
import { ClinicMapContent } from "@/features/clinic-map/ui/ClinicMapContent";
import { PortalStaffCreate } from "@/features/setting-portal/ui/staff/PortalStaffCreate";
import { useGetUserPermissions } from "@/hooks/useGetUserPermissions";

const StaffCreatePage = () => {
  const { isSessionLoading, pages } = useGetUserPermissions();

  if (isSessionLoading) return null;

  // クエリを実行させないためルートファイルで判定
  if (!pages.settingPortalEnabled) {
    return (
      <ClinicMapContent>
        <NoPermission />
      </ClinicMapContent>
    );
  }

  return (
    <ClinicMapContent>
      <PortalStaffCreate />
    </ClinicMapContent>
  );
};

export default WithClinicAuth(StaffCreatePage);
