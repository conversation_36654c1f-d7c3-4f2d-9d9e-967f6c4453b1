import { gql } from "@/apis/gql/apollo-client";

export const GET_SYSTEM_NOTIFICATIONS = gql`
  query getSystemNotifications {
    getSystemNotifications {
      systemNotificationID
      tagList
      title
      description
      createdAt
    }
  }
`;

export const GET_READ_SYSTEM_NOTIFICATIONS = gql`
  query getReadSystemNotifications {
    getReadSystemNotifications
  }
`;

export const EDIT_READ_SYSTEM_NOTIFICATIONS = gql`
  mutation editReadSystemNotifications($input: [Int!]!) {
    editReadSystemNotifications(input: $input)
  }
`;
