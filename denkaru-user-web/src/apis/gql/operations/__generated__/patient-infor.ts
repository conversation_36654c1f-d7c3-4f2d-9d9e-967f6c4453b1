import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiPatientInforGetInsuranceMstQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiPatientInforGetInsuranceMstQuery = {
  __typename?: "query_root";
  getApiPatientInforGetInsuranceMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceMstGetInsuranceMstResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceMstGetInsuranceMstResponse";
      prefNo?: number;
      insuranceMst?: {
        __typename?: "DomainModelsInsuranceMstInsuranceMstModel";
        hokenMstAlLData?: Array<{
          __typename?: "DomainModelsInsuranceMstHokenMstModel";
          futanKbn?: number;
          futanRate?: number;
          startDate?: number;
          endDate?: number;
          hokenNo?: number;
          hokenEdaNo?: number;
          hokenSName?: string;
          houbetu?: string;
          hokenSbtKbn?: number;
          checkDigit?: number;
          ageStart?: number;
          ageEnd?: number;
          isFutansyaNoCheck?: number;
          isJyukyusyaNoCheck?: number;
          jyuKyuCheckDigit?: number;
          isTokusyuNoCheck?: number;
          hokenName?: string;
          hokenNameCd?: string;
          hokenKohiKbn?: number;
          isOtherPrefValid?: number;
          receKisai?: number;
          isLimitList?: number;
          isLimitListSum?: number;
          enTen?: number;
          kaiLimitFutan?: number;
          dayLimitFutan?: number;
          monthLimitFutan?: number;
          monthLimitCount?: number;
          limitKbn?: number;
          countKbn?: number;
          futanYusen?: number;
          calcSpKbn?: number;
          monthSpLimit?: number;
          kogakuTekiyo?: number;
          kogakuTotalKbn?: number;
          kogakuHairyoKbn?: number;
          receSeikyuKbn?: number;
          receKisaiKokho?: number;
          receKisai2?: number;
          receTenKisai?: number;
          receFutanRound?: number;
          receZeroKisai?: number;
          receSpKbn?: number;
          prefactureName?: string;
          prefNo?: number;
          sortNo?: number;
          seikyuYm?: number;
          receFutanHide?: number;
          receFutanKbn?: number;
          kogakuTotalAll?: number;
          kogakuTotalExcFutan?: number;
          kaiFutangaku?: number;
          isAdded?: boolean;
          dayLimitCount?: number;
          selectedValueMaster?: string;
          displayTextMaster?: string;
          displayHokenNo?: string;
          moneyLimitListFlag?: number;
          houbetuDisplayText?: string;
          displayTextMasterWithoutHokenNo?: string;
          excepHokenSyas?: Array<{
            __typename?: "DomainModelsInsuranceMstExceptHokensyaModel";
            id?: string;
            hpId?: number;
            prefNo?: number;
            hokenNo?: number;
            hokenEdaNo?: number;
            startDate?: number;
            hokensyaNo?: string;
          }>;
        }>;
      };
    };
  };
};

export type GetApiPatientInforGetTokkiMstListQueryVariables = Types.Exact<{
  seikyuYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiPatientInforGetTokkiMstListQuery = {
  __typename?: "query_root";
  getApiPatientInforGetTokkiMstList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforGetTokkiMstListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforGetTokkiMstListResponse";
      tokkiMstList?: Array<{
        __typename?: "EmrCloudApiResponsesPatientInforTokkiMstDto";
        tokkiCd?: string;
        tokkiName?: string;
        displayTextMst?: string;
      }>;
    };
  };
};

export type GetApiPatientInforSearchPatientInfoByPtNumQueryVariables =
  Types.Exact<{
    ptNum?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type GetApiPatientInforSearchPatientInfoByPtNumQuery = {
  __typename?: "query_root";
  getApiPatientInforSearchPatientInfoByPtNum?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforSearchPatientInfoByPtNumResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforSearchPatientInfoByPtNumResponse";
      patientInfor?: {
        __typename?: "DomainModelsPatientInforPatientInforModel";
        hpId?: number;
        ptId?: string;
        ptNum?: string;
        kanaName?: string;
        name?: string;
        seqNo?: string;
        referenceNo?: string;
        sex?: number;
        birthday?: number;
        limitConsFlg?: number;
        isDead?: number;
        deathDate?: number;
        homePost?: string;
        homeAddress1?: string;
        homeAddress2?: string;
        tel1?: string;
        tel2?: string;
        mail?: string;
        setanusi?: string;
        zokugara?: string;
        job?: string;
        renrakuName?: string;
        renrakuPost?: string;
        renrakuAddress1?: string;
        renrakuAddress2?: string;
        renrakuTel?: string;
        renrakuMemo?: string;
        officeName?: string;
        officePost?: string;
        officeAddress1?: string;
        officeAddress2?: string;
        officeTel?: string;
        officeMemo?: string;
        isRyosyoDetail?: number;
        primaryDoctor?: number;
        isTester?: number;
        mainHokenPid?: number;
        memo?: string;
        firstVisitDate?: number;
        rainCountInt?: number;
        rainCount?: string;
        comment?: string;
        lastVisitDate?: number;
        isShowKyuSeiName?: boolean;
        sinDate?: number;
        birthdayDisplay?: string;
        age?: string;
      };
    };
  };
};

export type GetApiPatientInforGetPatientByIdQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  listStatus?: Types.InputMaybe<
    Array<Types.Scalars["Int"]["input"]> | Types.Scalars["Int"]["input"]
  >;
  isShowKyuSeiName?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetApiPatientInforGetPatientByIdQuery = {
  __typename?: "query_root";
  getApiPatientInforGetPatientById?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInformaitonGetPatientInforByIdResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInformaitonGetPatientInforByIdResponse";
      data?: {
        __typename?: "DomainModelsPatientInforPatientInforModel";
        age?: string;
        birthday?: number;
        birthdayDisplay?: string;
        comment?: string;
        deathDate?: number;
        firstVisitDate?: number;
        homeAddress1?: string;
        homeAddress2?: string;
        homePost?: string;
        hpId?: number;
        isDead?: number;
        isRyosyoDetail?: number;
        isShowKyuSeiName?: boolean;
        isTester?: number;
        job?: string;
        kanaName?: string;
        lastVisitDate?: number;
        limitConsFlg?: number;
        mail?: string;
        mainHokenPid?: number;
        memo?: string;
        name?: string;
        officeAddress1?: string;
        officeAddress2?: string;
        officeMemo?: string;
        officeName?: string;
        officePost?: string;
        officeTel?: string;
        primaryDoctor?: number;
        ptId?: string;
        ptNum?: string;
        rainCount?: string;
        rainCountInt?: number;
        referenceNo?: string;
        renrakuAddress1?: string;
        renrakuAddress2?: string;
        renrakuMemo?: string;
        renrakuName?: string;
        renrakuPost?: string;
        renrakuTel?: string;
        seqNo?: string;
        setanusi?: string;
        sex?: number;
        sinDate?: number;
        tel1?: string;
        tel2?: string;
        zokugara?: string;
        renrakuTel2?: string;
        renrakuName2?: string;
        portalCustomerId?: number;
      };
    };
  };
};

export type GetApiPatientInforGetHokenSyaMstQueryVariables = Types.Exact<{
  keyword?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiPatientInforGetHokenSyaMstQuery = {
  __typename?: "query_root";
  getApiPatientInforSearchHokensyaMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceMstSearchHokensyaMstResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceMstSearchHokensyaMstResponse";
      listData?: Array<{
        __typename?: "DomainModelsInsuranceMstHokensyaMstModel";
        hpId?: number;
        name?: string;
        kanaName?: string;
        houbetuKbn?: string;
        houbetu?: string;
        hokenKbn?: number;
        prefNo?: number;
        hokensyaNo?: string;
        kigo?: string;
        bango?: string;
        rateHonnin?: number;
        rateKazoku?: number;
        postCode?: string;
        address1?: string;
        address2?: string;
        tel1?: string;
        isKigoNa?: number;
        isReadOnlyHokenSyaNo?: boolean;
        postCdDisplay?: string;
      }>;
    };
  };
};

export type GetApiPatientInforSearchSimpleQueryVariables = Types.Exact<{
  keyword?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  isContainMode?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  pageIndex?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  pageSize?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sortData?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.EmrCloudApiRequestsPatientInforSortColInput>>
    | Types.InputMaybe<Types.EmrCloudApiRequestsPatientInforSortColInput>
  >;
}>;

export type GetApiPatientInforSearchSimpleQuery = {
  __typename?: "query_root";
  getApiPatientInforSearchSimple?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInformaitonSearchPatientInforSimpleResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInformaitonSearchPatientInforSimpleResponse";
      data?: Array<{
        __typename?: "UseCasePatientInforPatientInfoWithGroup";
        ptId?: string;
        ptNum?: string;
        kanaName?: string;
        name?: string;
        birthday?: string;
        birthdayRaw?: number;
        sex?: number;
        age?: string;
        tel1?: string;
        tel2?: string;
        renrakuTel?: string;
        homePost?: string;
        homeAddress?: string;
        lastVisitDate?: string;
        birthdayDisplay?: string;
      }>;
    };
  };
};

export type GetApiPatientInforGetPatientInfoBetweenTimesListQueryVariables =
  Types.Exact<{
    simYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    startDateD?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    startTimeH?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    startTimeM?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    endDateD?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    endTimeH?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    endTimeM?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type GetApiPatientInforGetPatientInfoBetweenTimesListQuery = {
  __typename?: "query_root";
  getApiPatientInforGetPatientInfoBetweenTimesList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforGetPatientInfoBetweenTimesListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforGetPatientInfoBetweenTimesListResponse";
      patientInfoList?: Array<{
        __typename?: "UseCasePatientInforGetPatientInfoBetweenTimesListPatientInfoOutputItem";
        ptId?: string;
        ptNum?: string;
        kanaName?: string;
        name?: string;
        sex?: number;
      }>;
    };
  };
};

export type GetApiPatientInforGetInsurancesDataByPtIdQueryVariables =
  Types.Exact<{
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    isOdrInf?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  }>;

export type GetApiPatientInforGetInsurancesDataByPtIdQuery = {
  __typename?: "query_root";
  getApiPatientInforGetHokenPatternByPtId?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceAiChatGetHokenPatternByPtIdResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceAiChatGetHokenPatternByPtIdResponse";
      data?: Array<{
        __typename?: "DomainModelsInsuranceAiChatHokenPatternDto";
        patternName?: string;
        hokenKbn?: number;
        hokenPid?: number;
        isUsedPattern?: number;
        sinDate?: number;
        sinDateRecentUse?: number;
        usedPattern?: {
          __typename?: "DomainModelsPatientInforUsedPattern";
          hokenPid?: number;
          orderCount?: number;
        };
        hokenInf?: {
          __typename?: "DomainModelsInsuranceAiChatHokenInfDto";
          bango?: string;
          checkDate?: number;
          edaNo?: string;
          endDate?: number;
          hokenNo?: number;
          hokenSName?: string;
          hokensyaNo?: string;
          kigo?: string;
          startDate?: number;
          hokenId?: number;
          onlineConfirmCheckDate?: number;
          isExpirated?: boolean;
          rousaiKofuNo?: string;
          jibaiHokenName?: string;
          hokenKbn?: number;
          seqNo?: string;
          hokenCheck?: Array<{
            __typename?: "DomainModelsInsuranceAiChatHokenCheckDto";
            checkComment?: string;
            checkDate?: number;
            checkId?: number;
            checkMachine?: string;
            checkName?: string;
            hokenGrp?: number;
            hokenId?: number;
            isDeleted?: number;
            ptId?: string;
            seqNo?: string;
            onlineConfirmationId?: number;
          }>;
        };
        kohi1?: {
          __typename?: "DomainModelsInsuranceAiChatKohiInfDto";
          checkDate?: number;
          sinDate?: number;
          endDate?: number;
          isEmptyModel?: boolean;
          kohiName?: string;
          houbetu?: string;
          futansyaNo?: string;
          jyukyusyaNo?: string;
          tokusyuNo?: string;
          onlineConfirmCheckDate?: number;
          hokenId?: number;
          isExpirated?: boolean;
          startDate?: number;
          seqNo?: string;
          listHokenCheck?: Array<{
            __typename?: "DomainModelsInsuranceAiChatHokenCheckDto";
            checkComment?: string;
            checkDate?: number;
            checkId?: number;
            checkMachine?: string;
            checkName?: string;
            hokenGrp?: number;
            hokenId?: number;
            isDeleted?: number;
            ptId?: string;
            seqNo?: string;
            onlineConfirmationId?: number;
          }>;
        };
        kohi2?: {
          __typename?: "DomainModelsInsuranceAiChatKohiInfDto";
          checkDate?: number;
          sinDate?: number;
          endDate?: number;
          isEmptyModel?: boolean;
          kohiName?: string;
          houbetu?: string;
          futansyaNo?: string;
          jyukyusyaNo?: string;
          tokusyuNo?: string;
          onlineConfirmCheckDate?: number;
          hokenId?: number;
          isExpirated?: boolean;
          startDate?: number;
          seqNo?: string;
          listHokenCheck?: Array<{
            __typename?: "DomainModelsInsuranceAiChatHokenCheckDto";
            checkComment?: string;
            checkDate?: number;
            checkId?: number;
            checkMachine?: string;
            checkName?: string;
            hokenGrp?: number;
            hokenId?: number;
            isDeleted?: number;
            ptId?: string;
            seqNo?: string;
            onlineConfirmationId?: number;
          }>;
        };
        kohi3?: {
          __typename?: "DomainModelsInsuranceAiChatKohiInfDto";
          checkDate?: number;
          sinDate?: number;
          endDate?: number;
          isEmptyModel?: boolean;
          kohiName?: string;
          houbetu?: string;
          futansyaNo?: string;
          jyukyusyaNo?: string;
          tokusyuNo?: string;
          onlineConfirmCheckDate?: number;
          hokenId?: number;
          isExpirated?: boolean;
          startDate?: number;
          seqNo?: string;
          listHokenCheck?: Array<{
            __typename?: "DomainModelsInsuranceAiChatHokenCheckDto";
            checkComment?: string;
            checkDate?: number;
            checkId?: number;
            checkMachine?: string;
            checkName?: string;
            hokenGrp?: number;
            hokenId?: number;
            isDeleted?: number;
            ptId?: string;
            seqNo?: string;
            onlineConfirmationId?: number;
          }>;
        };
        kohi4?: {
          __typename?: "DomainModelsInsuranceAiChatKohiInfDto";
          checkDate?: number;
          sinDate?: number;
          endDate?: number;
          isEmptyModel?: boolean;
          kohiName?: string;
          houbetu?: string;
          futansyaNo?: string;
          jyukyusyaNo?: string;
          tokusyuNo?: string;
          onlineConfirmCheckDate?: number;
          hokenId?: number;
          isExpirated?: boolean;
          startDate?: number;
          seqNo?: string;
          listHokenCheck?: Array<{
            __typename?: "DomainModelsInsuranceAiChatHokenCheckDto";
            checkComment?: string;
            checkDate?: number;
            checkId?: number;
            checkMachine?: string;
            checkName?: string;
            hokenGrp?: number;
            hokenId?: number;
            isDeleted?: number;
            ptId?: string;
            seqNo?: string;
            onlineConfirmationId?: number;
          }>;
        };
      }>;
    };
  };
  getApiPatientInforGetHokenInfByPtId?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceAiChatGetHokenInfByPtIdResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceAiChatGetHokenInfByPtIdResponse";
      data?: Array<{
        __typename?: "DomainModelsInsuranceAiChatHokenInfDto";
        bango?: string;
        edaNo?: string;
        endDate?: number;
        kigo?: string;
        startDate?: number;
        checkDate?: number;
        hokenSName?: string;
        hokensyaNo?: string;
        onlineConfirmCheckDate?: number;
        hokenNo?: number;
        hokenId?: number;
        isExpirated?: boolean;
        hokenSentaku?: string;
        hokenKbn?: number;
        rousaiKofuNo?: string;
        jibaiHokenName?: string;
        seqNo?: string;
        houbetu?: string;
        hokenCheck?: Array<{
          __typename?: "DomainModelsInsuranceAiChatHokenCheckDto";
          checkComment?: string;
          checkDate?: number;
          checkId?: number;
          checkMachine?: string;
          checkName?: string;
          hokenGrp?: number;
          hokenId?: number;
          isDeleted?: number;
          ptId?: string;
          seqNo?: string;
          onlineConfirmationId?: number;
        }>;
      }>;
    };
  };
  getApiPatientInforGetKohiInfByPtId?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceAiChatGetKohiInfByPtIdResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceAiChatGetKohiInfByPtIdResponse";
      data?: Array<{
        __typename?: "DomainModelsInsuranceAiChatKohiInfDto";
        checkDate?: number;
        sinDate?: number;
        endDate?: number;
        isEmptyModel?: boolean;
        kohiName?: string;
        houbetu?: string;
        futansyaNo?: string;
        jyukyusyaNo?: string;
        tokusyuNo?: string;
        onlineConfirmCheckDate?: number;
        hokenId?: number;
        isExpirated?: boolean;
        startDate?: number;
        prefNo?: number;
        hokenSbtKbn?: number;
        seqNo?: string;
        listHokenCheck?: Array<{
          __typename?: "DomainModelsInsuranceAiChatHokenCheckDto";
          checkComment?: string;
          checkDate?: number;
          checkId?: number;
          checkMachine?: string;
          checkName?: string;
          hokenGrp?: number;
          hokenId?: number;
          isDeleted?: number;
          ptId?: string;
          seqNo?: string;
          onlineConfirmationId?: number;
        }>;
      }>;
    };
  };
};

export type GetApiPatientInforGetPtKyuseiInfQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  isDeleted?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetApiPatientInforGetPtKyuseiInfQuery = {
  __typename?: "query_root";
  getApiPatientInforGetPtKyuseiInf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforPtKyuseiInfGetPtKyuseiInfResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforPtKyuseiInfGetPtKyuseiInfResponse";
      ptKyuseiInfModels?: Array<{
        __typename?: "DomainModelsPatientInforPtKyuseiInfModel";
        endDate?: number;
        kanaName?: string;
        name?: string;
        isDeleted?: number;
        seqNo?: string;
      }>;
    };
  };
};

export type PostApiPatientInforSavePtKyuseiMutationVariables = Types.Exact<{
  emrCloudApiRequestsPatientInforPtKyuseiInfSavePtKyuseiRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsPatientInforPtKyuseiInfSavePtKyuseiRequestInput>;
}>;

export type PostApiPatientInforSavePtKyuseiMutation = {
  __typename?: "mutation_root";
  postApiPatientInforSavePtKyusei?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforPtKyuseiInfSavePtKyuseiResponse";
    message?: string;
  };
};

export type PostApiPatientInforSavePatientMutationVariables = Types.Exact<{
  payload?: Types.InputMaybe<Types.EmrCloudApiRequestsPatientInforBasicPatientInfoSaveBasicPatientInfoRequestInput>;
}>;

export type PostApiPatientInforSavePatientMutation = {
  __typename?: "mutation_root";
  postApiPatientInforSavePatient?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforBasicPatientInfoSaveBasicPatientInfoResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforBasicPatientInfoSaveBasicPatientInfoResponse";
      state?: number;
      ptID?: string;
      raiinNo?: string;
      validateDetails?: Array<{
        __typename?: "UseCasePatientInforSaveSavePatientInfoValidationResult";
        code?: number;
        fieldName?: string;
        message?: string;
        listPtNum?: Array<string>;
        type?: number;
      }>;
    };
  };
};

export type PostApiPatientInforValidateHokenMutationVariables = Types.Exact<{
  payload?: Types.InputMaybe<Types.EmrCloudApiRequestsPatientInforValidateHokenRequestInput>;
}>;

export type PostApiPatientInforValidateHokenMutation = {
  __typename?: "mutation_root";
  postApiPatientInforValidateHoken?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceValidateHokenResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceValidateHokenResponse";
      resultCheck?: boolean;
      detail?: Array<{
        __typename?: "DomainModelsInsuranceResultValidateHokenPattern1UseCaseInsuranceValidateHokenInfValidHokenInfStatus";
        displayTextMst?: string;
        endDate?: number;
        fieldName?: string;
        insuranceTitle?: string;
        insuranceType?: string;
        messageContent?: string;
        messageTitle?: string;
        startDate?: number;
        status?: number;
        title?: string;
        typeMessage?: number;
      }>;
    };
  };
};

export type GetApiPatientInforValidateHokenPatternQueryVariables = Types.Exact<{
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi2Id?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi1Id?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi3Id?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi4Id?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptBirthday?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiPatientInforValidateHokenPatternQuery = {
  __typename?: "query_root";
  getApiPatientInforValidateHokenPattern?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceValidateHokenPatternResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceValidateHokenPatternResponse";
      resultCheck?: boolean;
      detail?: Array<{
        __typename?: "DomainModelsInsuranceResultValidateHokenPattern1UseCaseInsuranceValidateHokenPatternValidHokenPatternStatus";
        displayTextMst?: string;
        endDate?: number;
        insuranceTitle?: string;
        insuranceType?: string;
        messageContent?: string;
        messageTitle?: string;
        startDate?: number;
        status?: number;
        title?: string;
        typeMessage?: number;
      }>;
    };
  };
};

export type GetApiPatientInforGetHokenPatternByPtIdQueryVariables =
  Types.Exact<{
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type GetApiPatientInforGetHokenPatternByPtIdQuery = {
  __typename?: "query_root";
  getApiPatientInforGetHokenPatternByPtId?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceAiChatGetHokenPatternByPtIdResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceAiChatGetHokenPatternByPtIdResponse";
      data?: Array<{
        __typename?: "DomainModelsInsuranceAiChatHokenPatternDto";
        patternName?: string;
        hokenKbn?: number;
        hokenPid?: number;
        sinDateRecentUse?: number;
        isDefault?: boolean;
        hokenInf?: {
          __typename?: "DomainModelsInsuranceAiChatHokenInfDto";
          bango?: string;
          checkDate?: number;
          edaNo?: string;
          endDate?: number;
          hokenNo?: number;
          hokenSName?: string;
          hokensyaNo?: string;
          kigo?: string;
          startDate?: number;
          hokenId?: number;
          onlineConfirmCheckDate?: number;
          isExpirated?: boolean;
          rousaiKofuNo?: string;
          jibaiHokenName?: string;
          hokenKbn?: number;
          seqNo?: string;
          hokenSentaku?: string;
        };
        kohi1?: {
          __typename?: "DomainModelsInsuranceAiChatKohiInfDto";
          checkDate?: number;
          sinDate?: number;
          endDate?: number;
          isEmptyModel?: boolean;
          kohiName?: string;
          houbetu?: string;
          futansyaNo?: string;
          jyukyusyaNo?: string;
          tokusyuNo?: string;
          onlineConfirmCheckDate?: number;
          hokenId?: number;
          isExpirated?: boolean;
          startDate?: number;
          seqNo?: string;
        };
        kohi2?: {
          __typename?: "DomainModelsInsuranceAiChatKohiInfDto";
          checkDate?: number;
          sinDate?: number;
          endDate?: number;
          isEmptyModel?: boolean;
          kohiName?: string;
          houbetu?: string;
          futansyaNo?: string;
          jyukyusyaNo?: string;
          tokusyuNo?: string;
          onlineConfirmCheckDate?: number;
          hokenId?: number;
          isExpirated?: boolean;
          startDate?: number;
          seqNo?: string;
        };
        kohi3?: {
          __typename?: "DomainModelsInsuranceAiChatKohiInfDto";
          checkDate?: number;
          sinDate?: number;
          endDate?: number;
          isEmptyModel?: boolean;
          kohiName?: string;
          houbetu?: string;
          futansyaNo?: string;
          jyukyusyaNo?: string;
          tokusyuNo?: string;
          onlineConfirmCheckDate?: number;
          hokenId?: number;
          isExpirated?: boolean;
          startDate?: number;
          seqNo?: string;
        };
        kohi4?: {
          __typename?: "DomainModelsInsuranceAiChatKohiInfDto";
          checkDate?: number;
          sinDate?: number;
          endDate?: number;
          isEmptyModel?: boolean;
          kohiName?: string;
          houbetu?: string;
          futansyaNo?: string;
          jyukyusyaNo?: string;
          tokusyuNo?: string;
          onlineConfirmCheckDate?: number;
          hokenId?: number;
          isExpirated?: boolean;
          startDate?: number;
          seqNo?: string;
        };
      }>;
    };
  };
};

export type PostApiPatientInforValidateKohiMutationVariables = Types.Exact<{
  payload?: Types.InputMaybe<Types.EmrCloudApiRequestsInsuranceValidateKohiRequestInput>;
}>;

export type PostApiPatientInforValidateKohiMutation = {
  __typename?: "mutation_root";
  postApiPatientInforValidateKohi?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceValidateKohiResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceValidateKohiResponse";
      resultCheck?: boolean;
      details?: Array<{
        __typename?: "DomainModelsInsuranceResultValidateHokenPattern1UseCaseInsuranceValidKohiValidKohiStatus";
        displayTextMst?: string;
        endDate?: number;
        fieldName?: string;
        insuranceTitle?: string;
        insuranceType?: string;
        messageContent?: string;
        messageTitle?: string;
        startDate?: number;
        status?: number;
        title?: string;
        typeMessage?: number;
      }>;
    };
  };
};

export type GetApiPatientInforCheckPatientInfoDifferenceQueryVariables =
  Types.Exact<{
    onlineConfHisId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  }>;

export type GetApiPatientInforCheckPatientInfoDifferenceQuery = {
  __typename?: "query_root";
  getApiPatientInforCheckPatientInfoDifference?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforCheckPatientInfoDifferenceStatusCheckPatientInfoDifferenceResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforCheckPatientInfoDifferenceStatusCheckPatientInfoDifferenceResponse";
      checkPatientInfoDifferenceModel?: {
        __typename?: "CheckPatientInfoDifferenceModel";
        address?: {
          __typename?: "AddressInfo";
          isMap?: boolean;
          value?: string;
        };
        birthDay?: {
          __typename?: "BirthDayInfo";
          isMap?: boolean;
          value?: number;
        };
        homePost?: {
          __typename?: "AddressInfo";
          isMap?: boolean;
          value?: string;
        };
        kanaName?: { __typename?: "NameInfo"; isMap?: boolean; value?: string };
        name?: { __typename?: "NameInfo"; isMap?: boolean; value?: string };
        setainusi?: {
          __typename?: "AddressInfo";
          isMap?: boolean;
          value?: string;
        };
        sex?: { __typename?: "SexInfo"; isMap?: boolean; value?: number };
      };
    };
  };
};

export type PostApiPatientInforCheckPatientHokenInfoDifferenceMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsPatientInforCheckPatientHokenInfoDifferenceRequestInput>;
  }>;

export type PostApiPatientInforCheckPatientHokenInfoDifferenceMutation = {
  __typename?: "mutation_root";
  postApiPatientInforCheckPatientHokenInfoDifference?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforCheckPatientHokenInfoDifferenceResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforCheckPatientHokenInfoDifferenceResponse";
      checkPatientHokenInfoDifference?: {
        __typename?: "DomainModelsPatientInforCheckPatientHokenInfoDifferenceDto";
        hokenId?: number;
        isMapAll?: boolean;
        seqNo?: string;
        hokenName?: string;
        bango?: {
          __typename?: "DomainModelsPatientInforBango";
          value?: string;
          isMap?: boolean;
          xmlValue?: string;
        };
        endDate?: {
          __typename?: "DomainModelsPatientInforEndDate";
          value?: number;
          isMap?: boolean;
          xmlValue?: number;
        };
        hokenEdaNo?: {
          __typename?: "DomainModelsPatientInforHokenEdaNo";
          value?: string;
          isMap?: boolean;
          xmlValue?: string;
        };
        hokenInfo?: {
          __typename?: "DomainModelsPatientInforHokenInfo";
          value?: string;
          isMap?: boolean;
          xmlValue?: string;
        };
        hokensyaNo?: {
          __typename?: "DomainModelsPatientInforHokensyaNo";
          value?: string;
          isMap?: boolean;
          xmlValue?: string;
        };
        honkeKbn?: {
          __typename?: "DomainModelsPatientInforHonkeKbn";
          value?: number;
          isMap?: boolean;
          xmlValue?: number;
        };
        kigo?: {
          __typename?: "DomainModelsPatientInforKigo";
          value?: string;
          isMap?: boolean;
          xmlValue?: string;
        };
        kofuDate?: {
          __typename?: "DomainModelsPatientInforKofuDate";
          value?: number;
          isMap?: boolean;
          xmlValue?: number;
        };
        kogakuKbn?: {
          __typename?: "DomainModelsPatientInforKogakuKbn";
          value?: string;
          isMap?: boolean;
          xmlValue?: string;
        };
        startDate?: {
          __typename?: "DomainModelsPatientInforStartDate";
          value?: number;
          isMap?: boolean;
          xmlValue?: number;
        };
      };
    };
  };
};

export type PostApiPatientInforCheckKohiInfoDifferenceMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsPatientInforCheckKohiInfoDifferenceRequestInput>;
  }>;

export type PostApiPatientInforCheckKohiInfoDifferenceMutation = {
  __typename?: "mutation_root";
  postApiPatientInforCheckKohiInfoDifference?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforCheckKohiInfoDifferenceResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforCheckKohiInfoDifferenceResponse";
      checkKohiInfoDifference?: {
        __typename?: "DomainModelsPatientInforCheckKohiInfoDifferenceDto";
        hokenId?: number;
        isMapAll?: boolean;
        seqNo?: string;
        kohiName?: string;
        birthDay?: {
          __typename?: "DomainModelsPatientInforBirthDayInformation";
          isMap?: boolean;
          value?: number;
          xmlValue?: number;
        };
        endDate?: {
          __typename?: "DomainModelsPatientInforEndDateInfo";
          isMap?: boolean;
          value?: number;
          xmlValue?: number;
        };
        futansyaNo?: {
          __typename?: "DomainModelsPatientInforFutansyaNoInfor";
          isMap?: boolean;
          value?: string;
          xmlValue?: string;
        };
        gendogaku?: {
          __typename?: "DomainModelsPatientInforGendogakuInfo";
          isMap?: boolean;
          value?: number;
          xmlValue?: number;
        };
        hokenEdaNo?: {
          __typename?: "DomainModelsPatientInforHokenEdaNo";
          value?: string;
          isMap?: boolean;
          xmlValue?: string;
        };
        jyukyusyaNo?: {
          __typename?: "DomainModelsPatientInforJyukyusyaNoInfor";
          isMap?: boolean;
          value?: string;
          xmlValue?: string;
        };
        startDate?: {
          __typename?: "DomainModelsPatientInforStartDateInfo";
          isMap?: boolean;
          value?: number;
          xmlValue?: number;
        };
      };
    };
  };
};

export type PostApiPatientInforCheckPmhKohiInfoDifferenceMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsPatientInforCheckPmhKohiInfoDifferenceRequestInput>;
  }>;

export type PostApiPatientInforCheckPmhKohiInfoDifferenceMutation = {
  __typename?: "mutation_root";
  postApiPatientInforCheckPmhKohiInfoDifference?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforCheckPmhKohiInfoDifferenceResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforCheckPmhKohiInfoDifferenceResponse";
      checkPmhKohiInfoDifferenceDto?: {
        __typename?: "DomainModelsPatientInforCheckPmhKohiInfoDifferenceDto";
        hokenId?: number;
        hokenName?: string;
        isContinue?: boolean;
        isNotExistKohiCompare?: boolean;
        isMapAll?: boolean;
        seqNo?: string;
        pmhKohiInfoDifferenceProperties?: Array<{
          __typename?: "DomainModelsPatientInforPmhKohiInfoDifferenceProperty";
          index?: number;
          isMap?: boolean;
          kohiValue?: string;
          name?: string;
          xmlValue?: string;
        }>;
      };
    };
  };
};

export type PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsPatientInforSaveOnlineMyCardBeforeReceptionRequestInput>;
  }>;

export type PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutation = {
  __typename?: "mutation_root";
  postApiPatientInforSaveOnlineMyCardBeforeReception?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiRequestsAddPatientWithInsuranceSaveOnlineMyCardBeforeReceptionRes";
    data?: {
      __typename?: "EmrCloudApiRequestsAddPatientWithInsuranceSaveOnlineMyCardBeforeReceptionRes";
      hokenIds?: Array<number>;
      kohiIds?: Array<number>;
      status?: number;
    };
  };
};

export const GetApiPatientInforGetInsuranceMstDocument = gql`
  query getApiPatientInforGetInsuranceMst($ptId: BigInt, $sinDate: Int) {
    getApiPatientInforGetInsuranceMst(ptId: $ptId, sinDate: $sinDate) {
      data {
        insuranceMst {
          hokenMstAlLData {
            futanKbn
            futanRate
            startDate
            endDate
            hokenNo
            hokenEdaNo
            hokenSName
            houbetu
            hokenSbtKbn
            checkDigit
            ageStart
            ageEnd
            isFutansyaNoCheck
            isJyukyusyaNoCheck
            jyuKyuCheckDigit
            isTokusyuNoCheck
            hokenName
            hokenNameCd
            hokenKohiKbn
            isOtherPrefValid
            receKisai
            isLimitList
            isLimitListSum
            enTen
            kaiLimitFutan
            dayLimitFutan
            monthLimitFutan
            monthLimitCount
            limitKbn
            countKbn
            futanYusen
            calcSpKbn
            monthSpLimit
            kogakuTekiyo
            kogakuTotalKbn
            kogakuHairyoKbn
            receSeikyuKbn
            receKisaiKokho
            receKisai2
            receTenKisai
            receFutanRound
            receZeroKisai
            receSpKbn
            prefactureName
            prefNo
            sortNo
            seikyuYm
            receFutanHide
            receFutanKbn
            kogakuTotalAll
            kogakuTotalExcFutan
            kaiFutangaku
            isAdded
            dayLimitCount
            excepHokenSyas {
              id
              hpId
              prefNo
              hokenNo
              hokenEdaNo
              startDate
              hokensyaNo
            }
            selectedValueMaster
            displayTextMaster
            displayHokenNo
            moneyLimitListFlag
            houbetuDisplayText
            displayTextMasterWithoutHokenNo
          }
        }
        prefNo
      }
    }
  }
`;

/**
 * __useGetApiPatientInforGetInsuranceMstQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforGetInsuranceMstQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforGetInsuranceMstQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforGetInsuranceMstQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiPatientInforGetInsuranceMstQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforGetInsuranceMstQuery,
    GetApiPatientInforGetInsuranceMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforGetInsuranceMstQuery,
    GetApiPatientInforGetInsuranceMstQueryVariables
  >(GetApiPatientInforGetInsuranceMstDocument, options);
}
export function useGetApiPatientInforGetInsuranceMstLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforGetInsuranceMstQuery,
    GetApiPatientInforGetInsuranceMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforGetInsuranceMstQuery,
    GetApiPatientInforGetInsuranceMstQueryVariables
  >(GetApiPatientInforGetInsuranceMstDocument, options);
}
export function useGetApiPatientInforGetInsuranceMstSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforGetInsuranceMstQuery,
    GetApiPatientInforGetInsuranceMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforGetInsuranceMstQuery,
    GetApiPatientInforGetInsuranceMstQueryVariables
  >(GetApiPatientInforGetInsuranceMstDocument, options);
}
export type GetApiPatientInforGetInsuranceMstQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetInsuranceMstQuery
>;
export type GetApiPatientInforGetInsuranceMstLazyQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetInsuranceMstLazyQuery
>;
export type GetApiPatientInforGetInsuranceMstSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetInsuranceMstSuspenseQuery>;
export type GetApiPatientInforGetInsuranceMstQueryResult = Apollo.QueryResult<
  GetApiPatientInforGetInsuranceMstQuery,
  GetApiPatientInforGetInsuranceMstQueryVariables
>;
export const GetApiPatientInforGetTokkiMstListDocument = gql`
  query getApiPatientInforGetTokkiMstList($seikyuYm: Int) {
    getApiPatientInforGetTokkiMstList(seikyuYm: $seikyuYm) {
      data {
        tokkiMstList {
          tokkiCd
          tokkiName
          displayTextMst
        }
      }
    }
  }
`;

/**
 * __useGetApiPatientInforGetTokkiMstListQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforGetTokkiMstListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforGetTokkiMstListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforGetTokkiMstListQuery({
 *   variables: {
 *      seikyuYm: // value for 'seikyuYm'
 *   },
 * });
 */
export function useGetApiPatientInforGetTokkiMstListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforGetTokkiMstListQuery,
    GetApiPatientInforGetTokkiMstListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforGetTokkiMstListQuery,
    GetApiPatientInforGetTokkiMstListQueryVariables
  >(GetApiPatientInforGetTokkiMstListDocument, options);
}
export function useGetApiPatientInforGetTokkiMstListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforGetTokkiMstListQuery,
    GetApiPatientInforGetTokkiMstListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforGetTokkiMstListQuery,
    GetApiPatientInforGetTokkiMstListQueryVariables
  >(GetApiPatientInforGetTokkiMstListDocument, options);
}
export function useGetApiPatientInforGetTokkiMstListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforGetTokkiMstListQuery,
    GetApiPatientInforGetTokkiMstListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforGetTokkiMstListQuery,
    GetApiPatientInforGetTokkiMstListQueryVariables
  >(GetApiPatientInforGetTokkiMstListDocument, options);
}
export type GetApiPatientInforGetTokkiMstListQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetTokkiMstListQuery
>;
export type GetApiPatientInforGetTokkiMstListLazyQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetTokkiMstListLazyQuery
>;
export type GetApiPatientInforGetTokkiMstListSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetTokkiMstListSuspenseQuery>;
export type GetApiPatientInforGetTokkiMstListQueryResult = Apollo.QueryResult<
  GetApiPatientInforGetTokkiMstListQuery,
  GetApiPatientInforGetTokkiMstListQueryVariables
>;
export const GetApiPatientInforSearchPatientInfoByPtNumDocument = gql`
  query getApiPatientInforSearchPatientInfoByPtNum(
    $ptNum: BigInt
    $sinDate: Int
  ) {
    getApiPatientInforSearchPatientInfoByPtNum(
      ptNum: $ptNum
      sinDate: $sinDate
    ) {
      data {
        patientInfor {
          hpId
          ptId
          ptNum
          kanaName
          name
          seqNo
          referenceNo
          sex
          birthday
          limitConsFlg
          isDead
          deathDate
          homePost
          homeAddress1
          homeAddress2
          tel1
          tel2
          mail
          setanusi
          zokugara
          job
          renrakuName
          renrakuPost
          renrakuAddress1
          renrakuAddress2
          renrakuTel
          renrakuMemo
          officeName
          officePost
          officeAddress1
          officeAddress2
          officeTel
          officeMemo
          isRyosyoDetail
          primaryDoctor
          isTester
          mainHokenPid
          memo
          firstVisitDate
          rainCountInt
          rainCount
          comment
          lastVisitDate
          isShowKyuSeiName
          sinDate
          birthdayDisplay
          age
        }
      }
    }
  }
`;

/**
 * __useGetApiPatientInforSearchPatientInfoByPtNumQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforSearchPatientInfoByPtNumQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforSearchPatientInfoByPtNumQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforSearchPatientInfoByPtNumQuery({
 *   variables: {
 *      ptNum: // value for 'ptNum'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiPatientInforSearchPatientInfoByPtNumQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforSearchPatientInfoByPtNumQuery,
    GetApiPatientInforSearchPatientInfoByPtNumQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforSearchPatientInfoByPtNumQuery,
    GetApiPatientInforSearchPatientInfoByPtNumQueryVariables
  >(GetApiPatientInforSearchPatientInfoByPtNumDocument, options);
}
export function useGetApiPatientInforSearchPatientInfoByPtNumLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforSearchPatientInfoByPtNumQuery,
    GetApiPatientInforSearchPatientInfoByPtNumQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforSearchPatientInfoByPtNumQuery,
    GetApiPatientInforSearchPatientInfoByPtNumQueryVariables
  >(GetApiPatientInforSearchPatientInfoByPtNumDocument, options);
}
export function useGetApiPatientInforSearchPatientInfoByPtNumSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforSearchPatientInfoByPtNumQuery,
    GetApiPatientInforSearchPatientInfoByPtNumQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforSearchPatientInfoByPtNumQuery,
    GetApiPatientInforSearchPatientInfoByPtNumQueryVariables
  >(GetApiPatientInforSearchPatientInfoByPtNumDocument, options);
}
export type GetApiPatientInforSearchPatientInfoByPtNumQueryHookResult =
  ReturnType<typeof useGetApiPatientInforSearchPatientInfoByPtNumQuery>;
export type GetApiPatientInforSearchPatientInfoByPtNumLazyQueryHookResult =
  ReturnType<typeof useGetApiPatientInforSearchPatientInfoByPtNumLazyQuery>;
export type GetApiPatientInforSearchPatientInfoByPtNumSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPatientInforSearchPatientInfoByPtNumSuspenseQuery>;
export type GetApiPatientInforSearchPatientInfoByPtNumQueryResult =
  Apollo.QueryResult<
    GetApiPatientInforSearchPatientInfoByPtNumQuery,
    GetApiPatientInforSearchPatientInfoByPtNumQueryVariables
  >;
export const GetApiPatientInforGetPatientByIdDocument = gql`
  query getApiPatientInforGetPatientById(
    $ptId: BigInt
    $sinDate: Int
    $raiinNo: BigInt
    $listStatus: [Int!]
    $isShowKyuSeiName: Boolean
  ) {
    getApiPatientInforGetPatientById(
      ptId: $ptId
      sinDate: $sinDate
      raiinNo: $raiinNo
      listStatus: $listStatus
      isShowKyuSeiName: $isShowKyuSeiName
    ) {
      data {
        data {
          age
          birthday
          birthdayDisplay
          comment
          deathDate
          firstVisitDate
          homeAddress1
          homeAddress2
          homePost
          hpId
          isDead
          isRyosyoDetail
          isShowKyuSeiName
          isTester
          job
          kanaName
          lastVisitDate
          limitConsFlg
          mail
          mainHokenPid
          memo
          name
          officeAddress1
          officeAddress2
          officeMemo
          officeName
          officePost
          officeTel
          primaryDoctor
          ptId
          ptNum
          rainCount
          rainCountInt
          referenceNo
          renrakuAddress1
          renrakuAddress2
          renrakuMemo
          renrakuName
          renrakuPost
          renrakuTel
          seqNo
          setanusi
          sex
          sinDate
          tel1
          tel2
          zokugara
          renrakuTel2
          renrakuName2
          portalCustomerId
        }
      }
    }
  }
`;

/**
 * __useGetApiPatientInforGetPatientByIdQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforGetPatientByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforGetPatientByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforGetPatientByIdQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      raiinNo: // value for 'raiinNo'
 *      listStatus: // value for 'listStatus'
 *      isShowKyuSeiName: // value for 'isShowKyuSeiName'
 *   },
 * });
 */
export function useGetApiPatientInforGetPatientByIdQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforGetPatientByIdQuery,
    GetApiPatientInforGetPatientByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforGetPatientByIdQuery,
    GetApiPatientInforGetPatientByIdQueryVariables
  >(GetApiPatientInforGetPatientByIdDocument, options);
}
export function useGetApiPatientInforGetPatientByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforGetPatientByIdQuery,
    GetApiPatientInforGetPatientByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforGetPatientByIdQuery,
    GetApiPatientInforGetPatientByIdQueryVariables
  >(GetApiPatientInforGetPatientByIdDocument, options);
}
export function useGetApiPatientInforGetPatientByIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforGetPatientByIdQuery,
    GetApiPatientInforGetPatientByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforGetPatientByIdQuery,
    GetApiPatientInforGetPatientByIdQueryVariables
  >(GetApiPatientInforGetPatientByIdDocument, options);
}
export type GetApiPatientInforGetPatientByIdQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetPatientByIdQuery
>;
export type GetApiPatientInforGetPatientByIdLazyQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetPatientByIdLazyQuery
>;
export type GetApiPatientInforGetPatientByIdSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetPatientByIdSuspenseQuery>;
export type GetApiPatientInforGetPatientByIdQueryResult = Apollo.QueryResult<
  GetApiPatientInforGetPatientByIdQuery,
  GetApiPatientInforGetPatientByIdQueryVariables
>;
export const GetApiPatientInforGetHokenSyaMstDocument = gql`
  query getApiPatientInforGetHokenSyaMst($keyword: String, $sinDate: Int) {
    getApiPatientInforSearchHokensyaMst(keyword: $keyword, sinDate: $sinDate) {
      data {
        listData {
          hpId
          name
          kanaName
          houbetuKbn
          houbetu
          hokenKbn
          prefNo
          hokensyaNo
          kigo
          bango
          rateHonnin
          rateKazoku
          postCode
          address1
          address2
          tel1
          isKigoNa
          isReadOnlyHokenSyaNo
          postCdDisplay
        }
      }
    }
  }
`;

/**
 * __useGetApiPatientInforGetHokenSyaMstQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforGetHokenSyaMstQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforGetHokenSyaMstQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforGetHokenSyaMstQuery({
 *   variables: {
 *      keyword: // value for 'keyword'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiPatientInforGetHokenSyaMstQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforGetHokenSyaMstQuery,
    GetApiPatientInforGetHokenSyaMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforGetHokenSyaMstQuery,
    GetApiPatientInforGetHokenSyaMstQueryVariables
  >(GetApiPatientInforGetHokenSyaMstDocument, options);
}
export function useGetApiPatientInforGetHokenSyaMstLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforGetHokenSyaMstQuery,
    GetApiPatientInforGetHokenSyaMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforGetHokenSyaMstQuery,
    GetApiPatientInforGetHokenSyaMstQueryVariables
  >(GetApiPatientInforGetHokenSyaMstDocument, options);
}
export function useGetApiPatientInforGetHokenSyaMstSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforGetHokenSyaMstQuery,
    GetApiPatientInforGetHokenSyaMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforGetHokenSyaMstQuery,
    GetApiPatientInforGetHokenSyaMstQueryVariables
  >(GetApiPatientInforGetHokenSyaMstDocument, options);
}
export type GetApiPatientInforGetHokenSyaMstQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetHokenSyaMstQuery
>;
export type GetApiPatientInforGetHokenSyaMstLazyQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetHokenSyaMstLazyQuery
>;
export type GetApiPatientInforGetHokenSyaMstSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetHokenSyaMstSuspenseQuery>;
export type GetApiPatientInforGetHokenSyaMstQueryResult = Apollo.QueryResult<
  GetApiPatientInforGetHokenSyaMstQuery,
  GetApiPatientInforGetHokenSyaMstQueryVariables
>;
export const GetApiPatientInforSearchSimpleDocument = gql`
  query getApiPatientInforSearchSimple(
    $keyword: String
    $isContainMode: Boolean
    $pageIndex: Int
    $pageSize: Int
    $sortData: [EmrCloudApiRequestsPatientInforSortColInput]
  ) {
    getApiPatientInforSearchSimple(
      keyword: $keyword
      isContainMode: $isContainMode
      pageIndex: $pageIndex
      pageSize: $pageSize
      sortData: $sortData
    ) {
      data {
        data {
          ptId
          ptNum
          kanaName
          name
          birthday
          birthdayRaw
          sex
          age
          tel1
          tel2
          renrakuTel
          homePost
          homeAddress
          lastVisitDate
          birthdayDisplay
        }
      }
    }
  }
`;

/**
 * __useGetApiPatientInforSearchSimpleQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforSearchSimpleQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforSearchSimpleQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforSearchSimpleQuery({
 *   variables: {
 *      keyword: // value for 'keyword'
 *      isContainMode: // value for 'isContainMode'
 *      pageIndex: // value for 'pageIndex'
 *      pageSize: // value for 'pageSize'
 *      sortData: // value for 'sortData'
 *   },
 * });
 */
export function useGetApiPatientInforSearchSimpleQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforSearchSimpleQuery,
    GetApiPatientInforSearchSimpleQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforSearchSimpleQuery,
    GetApiPatientInforSearchSimpleQueryVariables
  >(GetApiPatientInforSearchSimpleDocument, options);
}
export function useGetApiPatientInforSearchSimpleLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforSearchSimpleQuery,
    GetApiPatientInforSearchSimpleQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforSearchSimpleQuery,
    GetApiPatientInforSearchSimpleQueryVariables
  >(GetApiPatientInforSearchSimpleDocument, options);
}
export function useGetApiPatientInforSearchSimpleSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforSearchSimpleQuery,
    GetApiPatientInforSearchSimpleQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforSearchSimpleQuery,
    GetApiPatientInforSearchSimpleQueryVariables
  >(GetApiPatientInforSearchSimpleDocument, options);
}
export type GetApiPatientInforSearchSimpleQueryHookResult = ReturnType<
  typeof useGetApiPatientInforSearchSimpleQuery
>;
export type GetApiPatientInforSearchSimpleLazyQueryHookResult = ReturnType<
  typeof useGetApiPatientInforSearchSimpleLazyQuery
>;
export type GetApiPatientInforSearchSimpleSuspenseQueryHookResult = ReturnType<
  typeof useGetApiPatientInforSearchSimpleSuspenseQuery
>;
export type GetApiPatientInforSearchSimpleQueryResult = Apollo.QueryResult<
  GetApiPatientInforSearchSimpleQuery,
  GetApiPatientInforSearchSimpleQueryVariables
>;
export const GetApiPatientInforGetPatientInfoBetweenTimesListDocument = gql`
  query getApiPatientInforGetPatientInfoBetweenTimesList(
    $simYm: Int
    $startDateD: Int
    $startTimeH: Int
    $startTimeM: Int
    $endDateD: Int
    $endTimeH: Int
    $endTimeM: Int
  ) {
    getApiPatientInforGetPatientInfoBetweenTimesList(
      sinYm: $simYm
      startDateD: $startDateD
      startTimeH: $startTimeH
      startTimeM: $startTimeM
      endDateD: $endDateD
      endTimeH: $endTimeH
      endTimeM: $endTimeM
    ) {
      data {
        patientInfoList {
          ptId
          ptNum
          kanaName
          name
          sex
        }
      }
    }
  }
`;

/**
 * __useGetApiPatientInforGetPatientInfoBetweenTimesListQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforGetPatientInfoBetweenTimesListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforGetPatientInfoBetweenTimesListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforGetPatientInfoBetweenTimesListQuery({
 *   variables: {
 *      simYm: // value for 'simYm'
 *      startDateD: // value for 'startDateD'
 *      startTimeH: // value for 'startTimeH'
 *      startTimeM: // value for 'startTimeM'
 *      endDateD: // value for 'endDateD'
 *      endTimeH: // value for 'endTimeH'
 *      endTimeM: // value for 'endTimeM'
 *   },
 * });
 */
export function useGetApiPatientInforGetPatientInfoBetweenTimesListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforGetPatientInfoBetweenTimesListQuery,
    GetApiPatientInforGetPatientInfoBetweenTimesListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforGetPatientInfoBetweenTimesListQuery,
    GetApiPatientInforGetPatientInfoBetweenTimesListQueryVariables
  >(GetApiPatientInforGetPatientInfoBetweenTimesListDocument, options);
}
export function useGetApiPatientInforGetPatientInfoBetweenTimesListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforGetPatientInfoBetweenTimesListQuery,
    GetApiPatientInforGetPatientInfoBetweenTimesListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforGetPatientInfoBetweenTimesListQuery,
    GetApiPatientInforGetPatientInfoBetweenTimesListQueryVariables
  >(GetApiPatientInforGetPatientInfoBetweenTimesListDocument, options);
}
export function useGetApiPatientInforGetPatientInfoBetweenTimesListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforGetPatientInfoBetweenTimesListQuery,
    GetApiPatientInforGetPatientInfoBetweenTimesListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforGetPatientInfoBetweenTimesListQuery,
    GetApiPatientInforGetPatientInfoBetweenTimesListQueryVariables
  >(GetApiPatientInforGetPatientInfoBetweenTimesListDocument, options);
}
export type GetApiPatientInforGetPatientInfoBetweenTimesListQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetPatientInfoBetweenTimesListQuery>;
export type GetApiPatientInforGetPatientInfoBetweenTimesListLazyQueryHookResult =
  ReturnType<
    typeof useGetApiPatientInforGetPatientInfoBetweenTimesListLazyQuery
  >;
export type GetApiPatientInforGetPatientInfoBetweenTimesListSuspenseQueryHookResult =
  ReturnType<
    typeof useGetApiPatientInforGetPatientInfoBetweenTimesListSuspenseQuery
  >;
export type GetApiPatientInforGetPatientInfoBetweenTimesListQueryResult =
  Apollo.QueryResult<
    GetApiPatientInforGetPatientInfoBetweenTimesListQuery,
    GetApiPatientInforGetPatientInfoBetweenTimesListQueryVariables
  >;
export const GetApiPatientInforGetInsurancesDataByPtIdDocument = gql`
  query getApiPatientInforGetInsurancesDataByPtId(
    $ptId: BigInt
    $sinDate: Int
    $isOdrInf: Boolean
  ) {
    getApiPatientInforGetHokenPatternByPtId(
      ptId: $ptId
      sinDate: $sinDate
      isOdrInf: $isOdrInf
    ) {
      data {
        data {
          patternName
          hokenKbn
          hokenPid
          usedPattern {
            hokenPid
            orderCount
          }
          isUsedPattern
          sinDate
          sinDateRecentUse
          hokenInf {
            bango
            checkDate
            edaNo
            endDate
            hokenNo
            hokenSName
            hokensyaNo
            kigo
            startDate
            hokenId
            onlineConfirmCheckDate
            isExpirated
            rousaiKofuNo
            jibaiHokenName
            hokenKbn
            seqNo
            hokenCheck {
              checkComment
              checkDate
              checkId
              checkMachine
              checkName
              hokenGrp
              hokenId
              isDeleted
              ptId
              seqNo
              onlineConfirmationId
            }
          }
          kohi1 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
            listHokenCheck {
              checkComment
              checkDate
              checkId
              checkMachine
              checkName
              hokenGrp
              hokenId
              isDeleted
              ptId
              seqNo
              onlineConfirmationId
            }
          }
          kohi2 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
            listHokenCheck {
              checkComment
              checkDate
              checkId
              checkMachine
              checkName
              hokenGrp
              hokenId
              isDeleted
              ptId
              seqNo
              onlineConfirmationId
            }
          }
          kohi3 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
            listHokenCheck {
              checkComment
              checkDate
              checkId
              checkMachine
              checkName
              hokenGrp
              hokenId
              isDeleted
              ptId
              seqNo
              onlineConfirmationId
            }
          }
          kohi4 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
            listHokenCheck {
              checkComment
              checkDate
              checkId
              checkMachine
              checkName
              hokenGrp
              hokenId
              isDeleted
              ptId
              seqNo
              onlineConfirmationId
            }
          }
        }
      }
    }
    getApiPatientInforGetHokenInfByPtId(ptId: $ptId, sinDate: $sinDate) {
      data {
        data {
          bango
          edaNo
          endDate
          kigo
          startDate
          checkDate
          hokenSName
          hokensyaNo
          onlineConfirmCheckDate
          hokenNo
          hokenId
          isExpirated
          hokenSentaku
          hokenKbn
          rousaiKofuNo
          jibaiHokenName
          seqNo
          hokenCheck {
            checkComment
            checkDate
            checkId
            checkMachine
            checkName
            hokenGrp
            hokenId
            isDeleted
            ptId
            seqNo
            onlineConfirmationId
          }
          houbetu
        }
      }
    }
    getApiPatientInforGetKohiInfByPtId(ptId: $ptId, sinDate: $sinDate) {
      data {
        data {
          checkDate
          sinDate
          endDate
          isEmptyModel
          kohiName
          houbetu
          futansyaNo
          jyukyusyaNo
          tokusyuNo
          onlineConfirmCheckDate
          hokenId
          isExpirated
          startDate
          prefNo
          hokenSbtKbn
          seqNo
          listHokenCheck {
            checkComment
            checkDate
            checkId
            checkMachine
            checkName
            hokenGrp
            hokenId
            isDeleted
            ptId
            seqNo
            onlineConfirmationId
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiPatientInforGetInsurancesDataByPtIdQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforGetInsurancesDataByPtIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforGetInsurancesDataByPtIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforGetInsurancesDataByPtIdQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      isOdrInf: // value for 'isOdrInf'
 *   },
 * });
 */
export function useGetApiPatientInforGetInsurancesDataByPtIdQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforGetInsurancesDataByPtIdQuery,
    GetApiPatientInforGetInsurancesDataByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforGetInsurancesDataByPtIdQuery,
    GetApiPatientInforGetInsurancesDataByPtIdQueryVariables
  >(GetApiPatientInforGetInsurancesDataByPtIdDocument, options);
}
export function useGetApiPatientInforGetInsurancesDataByPtIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforGetInsurancesDataByPtIdQuery,
    GetApiPatientInforGetInsurancesDataByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforGetInsurancesDataByPtIdQuery,
    GetApiPatientInforGetInsurancesDataByPtIdQueryVariables
  >(GetApiPatientInforGetInsurancesDataByPtIdDocument, options);
}
export function useGetApiPatientInforGetInsurancesDataByPtIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforGetInsurancesDataByPtIdQuery,
    GetApiPatientInforGetInsurancesDataByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforGetInsurancesDataByPtIdQuery,
    GetApiPatientInforGetInsurancesDataByPtIdQueryVariables
  >(GetApiPatientInforGetInsurancesDataByPtIdDocument, options);
}
export type GetApiPatientInforGetInsurancesDataByPtIdQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetInsurancesDataByPtIdQuery>;
export type GetApiPatientInforGetInsurancesDataByPtIdLazyQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetInsurancesDataByPtIdLazyQuery>;
export type GetApiPatientInforGetInsurancesDataByPtIdSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetInsurancesDataByPtIdSuspenseQuery>;
export type GetApiPatientInforGetInsurancesDataByPtIdQueryResult =
  Apollo.QueryResult<
    GetApiPatientInforGetInsurancesDataByPtIdQuery,
    GetApiPatientInforGetInsurancesDataByPtIdQueryVariables
  >;
export const GetApiPatientInforGetPtKyuseiInfDocument = gql`
  query getApiPatientInforGetPtKyuseiInf($ptId: BigInt, $isDeleted: Boolean) {
    getApiPatientInforGetPtKyuseiInf(isDeleted: $isDeleted, ptId: $ptId) {
      data {
        ptKyuseiInfModels {
          endDate
          kanaName
          name
          isDeleted
          seqNo
        }
      }
    }
  }
`;

/**
 * __useGetApiPatientInforGetPtKyuseiInfQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforGetPtKyuseiInfQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforGetPtKyuseiInfQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforGetPtKyuseiInfQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      isDeleted: // value for 'isDeleted'
 *   },
 * });
 */
export function useGetApiPatientInforGetPtKyuseiInfQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforGetPtKyuseiInfQuery,
    GetApiPatientInforGetPtKyuseiInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforGetPtKyuseiInfQuery,
    GetApiPatientInforGetPtKyuseiInfQueryVariables
  >(GetApiPatientInforGetPtKyuseiInfDocument, options);
}
export function useGetApiPatientInforGetPtKyuseiInfLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforGetPtKyuseiInfQuery,
    GetApiPatientInforGetPtKyuseiInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforGetPtKyuseiInfQuery,
    GetApiPatientInforGetPtKyuseiInfQueryVariables
  >(GetApiPatientInforGetPtKyuseiInfDocument, options);
}
export function useGetApiPatientInforGetPtKyuseiInfSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforGetPtKyuseiInfQuery,
    GetApiPatientInforGetPtKyuseiInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforGetPtKyuseiInfQuery,
    GetApiPatientInforGetPtKyuseiInfQueryVariables
  >(GetApiPatientInforGetPtKyuseiInfDocument, options);
}
export type GetApiPatientInforGetPtKyuseiInfQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetPtKyuseiInfQuery
>;
export type GetApiPatientInforGetPtKyuseiInfLazyQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetPtKyuseiInfLazyQuery
>;
export type GetApiPatientInforGetPtKyuseiInfSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetPtKyuseiInfSuspenseQuery>;
export type GetApiPatientInforGetPtKyuseiInfQueryResult = Apollo.QueryResult<
  GetApiPatientInforGetPtKyuseiInfQuery,
  GetApiPatientInforGetPtKyuseiInfQueryVariables
>;
export const PostApiPatientInforSavePtKyuseiDocument = gql`
  mutation postApiPatientInforSavePtKyusei(
    $emrCloudApiRequestsPatientInforPtKyuseiInfSavePtKyuseiRequestInput: EmrCloudApiRequestsPatientInforPtKyuseiInfSavePtKyuseiRequestInput
  ) {
    postApiPatientInforSavePtKyusei(
      emrCloudApiRequestsPatientInforPtKyuseiInfSavePtKyuseiRequestInput: $emrCloudApiRequestsPatientInforPtKyuseiInfSavePtKyuseiRequestInput
    ) {
      message
    }
  }
`;
export type PostApiPatientInforSavePtKyuseiMutationFn = Apollo.MutationFunction<
  PostApiPatientInforSavePtKyuseiMutation,
  PostApiPatientInforSavePtKyuseiMutationVariables
>;

/**
 * __usePostApiPatientInforSavePtKyuseiMutation__
 *
 * To run a mutation, you first call `usePostApiPatientInforSavePtKyuseiMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPatientInforSavePtKyuseiMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPatientInforSavePtKyuseiMutation, { data, loading, error }] = usePostApiPatientInforSavePtKyuseiMutation({
 *   variables: {
 *      emrCloudApiRequestsPatientInforPtKyuseiInfSavePtKyuseiRequestInput: // value for 'emrCloudApiRequestsPatientInforPtKyuseiInfSavePtKyuseiRequestInput'
 *   },
 * });
 */
export function usePostApiPatientInforSavePtKyuseiMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPatientInforSavePtKyuseiMutation,
    PostApiPatientInforSavePtKyuseiMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPatientInforSavePtKyuseiMutation,
    PostApiPatientInforSavePtKyuseiMutationVariables
  >(PostApiPatientInforSavePtKyuseiDocument, options);
}
export type PostApiPatientInforSavePtKyuseiMutationHookResult = ReturnType<
  typeof usePostApiPatientInforSavePtKyuseiMutation
>;
export type PostApiPatientInforSavePtKyuseiMutationResult =
  Apollo.MutationResult<PostApiPatientInforSavePtKyuseiMutation>;
export type PostApiPatientInforSavePtKyuseiMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPatientInforSavePtKyuseiMutation,
    PostApiPatientInforSavePtKyuseiMutationVariables
  >;
export const PostApiPatientInforSavePatientDocument = gql`
  mutation postApiPatientInforSavePatient(
    $payload: EmrCloudApiRequestsPatientInforBasicPatientInfoSaveBasicPatientInfoRequestInput
  ) {
    postApiPatientInforSavePatient(
      emrCloudApiRequestsPatientInforBasicPatientInfoSaveBasicPatientInfoRequestInput: $payload
    ) {
      data {
        state
        ptID
        raiinNo
        validateDetails {
          code
          fieldName
          message
          listPtNum
          type
        }
      }
    }
  }
`;
export type PostApiPatientInforSavePatientMutationFn = Apollo.MutationFunction<
  PostApiPatientInforSavePatientMutation,
  PostApiPatientInforSavePatientMutationVariables
>;

/**
 * __usePostApiPatientInforSavePatientMutation__
 *
 * To run a mutation, you first call `usePostApiPatientInforSavePatientMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPatientInforSavePatientMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPatientInforSavePatientMutation, { data, loading, error }] = usePostApiPatientInforSavePatientMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiPatientInforSavePatientMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPatientInforSavePatientMutation,
    PostApiPatientInforSavePatientMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPatientInforSavePatientMutation,
    PostApiPatientInforSavePatientMutationVariables
  >(PostApiPatientInforSavePatientDocument, options);
}
export type PostApiPatientInforSavePatientMutationHookResult = ReturnType<
  typeof usePostApiPatientInforSavePatientMutation
>;
export type PostApiPatientInforSavePatientMutationResult =
  Apollo.MutationResult<PostApiPatientInforSavePatientMutation>;
export type PostApiPatientInforSavePatientMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPatientInforSavePatientMutation,
    PostApiPatientInforSavePatientMutationVariables
  >;
export const PostApiPatientInforValidateHokenDocument = gql`
  mutation postApiPatientInforValidateHoken(
    $payload: EmrCloudApiRequestsPatientInforValidateHokenRequestInput
  ) {
    postApiPatientInforValidateHoken(
      emrCloudApiRequestsPatientInforValidateHokenRequestInput: $payload
    ) {
      data {
        detail {
          displayTextMst
          endDate
          fieldName
          insuranceTitle
          insuranceType
          messageContent
          messageTitle
          startDate
          status
          title
          typeMessage
        }
        resultCheck
      }
    }
  }
`;
export type PostApiPatientInforValidateHokenMutationFn =
  Apollo.MutationFunction<
    PostApiPatientInforValidateHokenMutation,
    PostApiPatientInforValidateHokenMutationVariables
  >;

/**
 * __usePostApiPatientInforValidateHokenMutation__
 *
 * To run a mutation, you first call `usePostApiPatientInforValidateHokenMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPatientInforValidateHokenMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPatientInforValidateHokenMutation, { data, loading, error }] = usePostApiPatientInforValidateHokenMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiPatientInforValidateHokenMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPatientInforValidateHokenMutation,
    PostApiPatientInforValidateHokenMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPatientInforValidateHokenMutation,
    PostApiPatientInforValidateHokenMutationVariables
  >(PostApiPatientInforValidateHokenDocument, options);
}
export type PostApiPatientInforValidateHokenMutationHookResult = ReturnType<
  typeof usePostApiPatientInforValidateHokenMutation
>;
export type PostApiPatientInforValidateHokenMutationResult =
  Apollo.MutationResult<PostApiPatientInforValidateHokenMutation>;
export type PostApiPatientInforValidateHokenMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPatientInforValidateHokenMutation,
    PostApiPatientInforValidateHokenMutationVariables
  >;
export const GetApiPatientInforValidateHokenPatternDocument = gql`
  query getApiPatientInforValidateHokenPattern(
    $hokenId: Int
    $kohi2Id: Int
    $kohi1Id: Int
    $kohi3Id: Int
    $kohi4Id: Int
    $ptBirthday: Int
    $ptId: BigInt
    $sinDate: Int
  ) {
    getApiPatientInforValidateHokenPattern(
      hokenId: $hokenId
      kohi2Id: $kohi2Id
      kohi1Id: $kohi1Id
      kohi3Id: $kohi3Id
      kohi4Id: $kohi4Id
      ptBirthday: $ptBirthday
      ptId: $ptId
      sinDate: $sinDate
    ) {
      data {
        detail {
          displayTextMst
          endDate
          insuranceTitle
          insuranceType
          messageContent
          messageTitle
          startDate
          status
          title
          typeMessage
        }
        resultCheck
      }
    }
  }
`;

/**
 * __useGetApiPatientInforValidateHokenPatternQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforValidateHokenPatternQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforValidateHokenPatternQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforValidateHokenPatternQuery({
 *   variables: {
 *      hokenId: // value for 'hokenId'
 *      kohi2Id: // value for 'kohi2Id'
 *      kohi1Id: // value for 'kohi1Id'
 *      kohi3Id: // value for 'kohi3Id'
 *      kohi4Id: // value for 'kohi4Id'
 *      ptBirthday: // value for 'ptBirthday'
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiPatientInforValidateHokenPatternQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforValidateHokenPatternQuery,
    GetApiPatientInforValidateHokenPatternQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforValidateHokenPatternQuery,
    GetApiPatientInforValidateHokenPatternQueryVariables
  >(GetApiPatientInforValidateHokenPatternDocument, options);
}
export function useGetApiPatientInforValidateHokenPatternLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforValidateHokenPatternQuery,
    GetApiPatientInforValidateHokenPatternQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforValidateHokenPatternQuery,
    GetApiPatientInforValidateHokenPatternQueryVariables
  >(GetApiPatientInforValidateHokenPatternDocument, options);
}
export function useGetApiPatientInforValidateHokenPatternSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforValidateHokenPatternQuery,
    GetApiPatientInforValidateHokenPatternQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforValidateHokenPatternQuery,
    GetApiPatientInforValidateHokenPatternQueryVariables
  >(GetApiPatientInforValidateHokenPatternDocument, options);
}
export type GetApiPatientInforValidateHokenPatternQueryHookResult = ReturnType<
  typeof useGetApiPatientInforValidateHokenPatternQuery
>;
export type GetApiPatientInforValidateHokenPatternLazyQueryHookResult =
  ReturnType<typeof useGetApiPatientInforValidateHokenPatternLazyQuery>;
export type GetApiPatientInforValidateHokenPatternSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPatientInforValidateHokenPatternSuspenseQuery>;
export type GetApiPatientInforValidateHokenPatternQueryResult =
  Apollo.QueryResult<
    GetApiPatientInforValidateHokenPatternQuery,
    GetApiPatientInforValidateHokenPatternQueryVariables
  >;
export const GetApiPatientInforGetHokenPatternByPtIdDocument = gql`
  query getApiPatientInforGetHokenPatternByPtId($ptId: BigInt, $sinDate: Int) {
    getApiPatientInforGetHokenPatternByPtId(ptId: $ptId, sinDate: $sinDate) {
      data {
        data {
          patternName
          hokenKbn
          hokenPid
          sinDateRecentUse
          isDefault
          hokenInf {
            bango
            checkDate
            edaNo
            endDate
            hokenNo
            hokenSName
            hokensyaNo
            kigo
            startDate
            hokenId
            onlineConfirmCheckDate
            isExpirated
            rousaiKofuNo
            jibaiHokenName
            hokenKbn
            seqNo
            hokenSentaku
          }
          kohi1 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
          }
          kohi2 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
          }
          kohi3 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
          }
          kohi4 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiPatientInforGetHokenPatternByPtIdQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforGetHokenPatternByPtIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforGetHokenPatternByPtIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforGetHokenPatternByPtIdQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiPatientInforGetHokenPatternByPtIdQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforGetHokenPatternByPtIdQuery,
    GetApiPatientInforGetHokenPatternByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforGetHokenPatternByPtIdQuery,
    GetApiPatientInforGetHokenPatternByPtIdQueryVariables
  >(GetApiPatientInforGetHokenPatternByPtIdDocument, options);
}
export function useGetApiPatientInforGetHokenPatternByPtIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforGetHokenPatternByPtIdQuery,
    GetApiPatientInforGetHokenPatternByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforGetHokenPatternByPtIdQuery,
    GetApiPatientInforGetHokenPatternByPtIdQueryVariables
  >(GetApiPatientInforGetHokenPatternByPtIdDocument, options);
}
export function useGetApiPatientInforGetHokenPatternByPtIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforGetHokenPatternByPtIdQuery,
    GetApiPatientInforGetHokenPatternByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforGetHokenPatternByPtIdQuery,
    GetApiPatientInforGetHokenPatternByPtIdQueryVariables
  >(GetApiPatientInforGetHokenPatternByPtIdDocument, options);
}
export type GetApiPatientInforGetHokenPatternByPtIdQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetHokenPatternByPtIdQuery
>;
export type GetApiPatientInforGetHokenPatternByPtIdLazyQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetHokenPatternByPtIdLazyQuery>;
export type GetApiPatientInforGetHokenPatternByPtIdSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetHokenPatternByPtIdSuspenseQuery>;
export type GetApiPatientInforGetHokenPatternByPtIdQueryResult =
  Apollo.QueryResult<
    GetApiPatientInforGetHokenPatternByPtIdQuery,
    GetApiPatientInforGetHokenPatternByPtIdQueryVariables
  >;
export const PostApiPatientInforValidateKohiDocument = gql`
  mutation postApiPatientInforValidateKohi(
    $payload: EmrCloudApiRequestsInsuranceValidateKohiRequestInput
  ) {
    postApiPatientInforValidateKohi(
      emrCloudApiRequestsInsuranceValidateKohiRequestInput: $payload
    ) {
      data {
        details {
          displayTextMst
          endDate
          fieldName
          insuranceTitle
          insuranceType
          messageContent
          messageTitle
          startDate
          status
          title
          typeMessage
        }
        resultCheck
      }
      message
      status
    }
  }
`;
export type PostApiPatientInforValidateKohiMutationFn = Apollo.MutationFunction<
  PostApiPatientInforValidateKohiMutation,
  PostApiPatientInforValidateKohiMutationVariables
>;

/**
 * __usePostApiPatientInforValidateKohiMutation__
 *
 * To run a mutation, you first call `usePostApiPatientInforValidateKohiMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPatientInforValidateKohiMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPatientInforValidateKohiMutation, { data, loading, error }] = usePostApiPatientInforValidateKohiMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiPatientInforValidateKohiMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPatientInforValidateKohiMutation,
    PostApiPatientInforValidateKohiMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPatientInforValidateKohiMutation,
    PostApiPatientInforValidateKohiMutationVariables
  >(PostApiPatientInforValidateKohiDocument, options);
}
export type PostApiPatientInforValidateKohiMutationHookResult = ReturnType<
  typeof usePostApiPatientInforValidateKohiMutation
>;
export type PostApiPatientInforValidateKohiMutationResult =
  Apollo.MutationResult<PostApiPatientInforValidateKohiMutation>;
export type PostApiPatientInforValidateKohiMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPatientInforValidateKohiMutation,
    PostApiPatientInforValidateKohiMutationVariables
  >;
export const GetApiPatientInforCheckPatientInfoDifferenceDocument = gql`
  query getApiPatientInforCheckPatientInfoDifference(
    $onlineConfHisId: BigInt
    $ptId: BigInt
  ) {
    getApiPatientInforCheckPatientInfoDifference(
      onlineConfHisId: $onlineConfHisId
      ptId: $ptId
    ) {
      data {
        checkPatientInfoDifferenceModel {
          address {
            isMap
            value
          }
          birthDay {
            isMap
            value
          }
          homePost {
            isMap
            value
          }
          kanaName {
            isMap
            value
          }
          name {
            isMap
            value
          }
          setainusi {
            isMap
            value
          }
          sex {
            isMap
            value
          }
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiPatientInforCheckPatientInfoDifferenceQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforCheckPatientInfoDifferenceQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforCheckPatientInfoDifferenceQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforCheckPatientInfoDifferenceQuery({
 *   variables: {
 *      onlineConfHisId: // value for 'onlineConfHisId'
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiPatientInforCheckPatientInfoDifferenceQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforCheckPatientInfoDifferenceQuery,
    GetApiPatientInforCheckPatientInfoDifferenceQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforCheckPatientInfoDifferenceQuery,
    GetApiPatientInforCheckPatientInfoDifferenceQueryVariables
  >(GetApiPatientInforCheckPatientInfoDifferenceDocument, options);
}
export function useGetApiPatientInforCheckPatientInfoDifferenceLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforCheckPatientInfoDifferenceQuery,
    GetApiPatientInforCheckPatientInfoDifferenceQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforCheckPatientInfoDifferenceQuery,
    GetApiPatientInforCheckPatientInfoDifferenceQueryVariables
  >(GetApiPatientInforCheckPatientInfoDifferenceDocument, options);
}
export function useGetApiPatientInforCheckPatientInfoDifferenceSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforCheckPatientInfoDifferenceQuery,
    GetApiPatientInforCheckPatientInfoDifferenceQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforCheckPatientInfoDifferenceQuery,
    GetApiPatientInforCheckPatientInfoDifferenceQueryVariables
  >(GetApiPatientInforCheckPatientInfoDifferenceDocument, options);
}
export type GetApiPatientInforCheckPatientInfoDifferenceQueryHookResult =
  ReturnType<typeof useGetApiPatientInforCheckPatientInfoDifferenceQuery>;
export type GetApiPatientInforCheckPatientInfoDifferenceLazyQueryHookResult =
  ReturnType<typeof useGetApiPatientInforCheckPatientInfoDifferenceLazyQuery>;
export type GetApiPatientInforCheckPatientInfoDifferenceSuspenseQueryHookResult =
  ReturnType<
    typeof useGetApiPatientInforCheckPatientInfoDifferenceSuspenseQuery
  >;
export type GetApiPatientInforCheckPatientInfoDifferenceQueryResult =
  Apollo.QueryResult<
    GetApiPatientInforCheckPatientInfoDifferenceQuery,
    GetApiPatientInforCheckPatientInfoDifferenceQueryVariables
  >;
export const PostApiPatientInforCheckPatientHokenInfoDifferenceDocument = gql`
  mutation postApiPatientInforCheckPatientHokenInfoDifference(
    $payload: EmrCloudApiRequestsPatientInforCheckPatientHokenInfoDifferenceRequestInput
  ) {
    postApiPatientInforCheckPatientHokenInfoDifference(
      emrCloudApiRequestsPatientInforCheckPatientHokenInfoDifferenceRequestInput: $payload
    ) {
      data {
        checkPatientHokenInfoDifference {
          hokenId
          isMapAll
          seqNo
          hokenName
          bango {
            value
            isMap
            xmlValue
          }
          endDate {
            value
            isMap
            xmlValue
          }
          hokenEdaNo {
            value
            isMap
            xmlValue
          }
          hokenInfo {
            value
            isMap
            xmlValue
          }
          hokensyaNo {
            value
            isMap
            xmlValue
          }
          honkeKbn {
            value
            isMap
            xmlValue
          }
          kigo {
            value
            isMap
            xmlValue
          }
          kofuDate {
            value
            isMap
            xmlValue
          }
          kogakuKbn {
            value
            isMap
            xmlValue
          }
          startDate {
            value
            isMap
            xmlValue
          }
        }
      }
    }
  }
`;
export type PostApiPatientInforCheckPatientHokenInfoDifferenceMutationFn =
  Apollo.MutationFunction<
    PostApiPatientInforCheckPatientHokenInfoDifferenceMutation,
    PostApiPatientInforCheckPatientHokenInfoDifferenceMutationVariables
  >;

/**
 * __usePostApiPatientInforCheckPatientHokenInfoDifferenceMutation__
 *
 * To run a mutation, you first call `usePostApiPatientInforCheckPatientHokenInfoDifferenceMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPatientInforCheckPatientHokenInfoDifferenceMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPatientInforCheckPatientHokenInfoDifferenceMutation, { data, loading, error }] = usePostApiPatientInforCheckPatientHokenInfoDifferenceMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiPatientInforCheckPatientHokenInfoDifferenceMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPatientInforCheckPatientHokenInfoDifferenceMutation,
    PostApiPatientInforCheckPatientHokenInfoDifferenceMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPatientInforCheckPatientHokenInfoDifferenceMutation,
    PostApiPatientInforCheckPatientHokenInfoDifferenceMutationVariables
  >(PostApiPatientInforCheckPatientHokenInfoDifferenceDocument, options);
}
export type PostApiPatientInforCheckPatientHokenInfoDifferenceMutationHookResult =
  ReturnType<
    typeof usePostApiPatientInforCheckPatientHokenInfoDifferenceMutation
  >;
export type PostApiPatientInforCheckPatientHokenInfoDifferenceMutationResult =
  Apollo.MutationResult<PostApiPatientInforCheckPatientHokenInfoDifferenceMutation>;
export type PostApiPatientInforCheckPatientHokenInfoDifferenceMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPatientInforCheckPatientHokenInfoDifferenceMutation,
    PostApiPatientInforCheckPatientHokenInfoDifferenceMutationVariables
  >;
export const PostApiPatientInforCheckKohiInfoDifferenceDocument = gql`
  mutation postApiPatientInforCheckKohiInfoDifference(
    $payload: EmrCloudApiRequestsPatientInforCheckKohiInfoDifferenceRequestInput
  ) {
    postApiPatientInforCheckKohiInfoDifference(
      emrCloudApiRequestsPatientInforCheckKohiInfoDifferenceRequestInput: $payload
    ) {
      data {
        checkKohiInfoDifference {
          hokenId
          isMapAll
          seqNo
          kohiName
          birthDay {
            isMap
            value
            xmlValue
          }
          endDate {
            isMap
            value
            xmlValue
          }
          futansyaNo {
            isMap
            value
            xmlValue
          }
          gendogaku {
            isMap
            value
            xmlValue
          }
          hokenEdaNo {
            value
            isMap
            xmlValue
          }
          jyukyusyaNo {
            isMap
            value
            xmlValue
          }
          startDate {
            isMap
            value
            xmlValue
          }
        }
      }
    }
  }
`;
export type PostApiPatientInforCheckKohiInfoDifferenceMutationFn =
  Apollo.MutationFunction<
    PostApiPatientInforCheckKohiInfoDifferenceMutation,
    PostApiPatientInforCheckKohiInfoDifferenceMutationVariables
  >;

/**
 * __usePostApiPatientInforCheckKohiInfoDifferenceMutation__
 *
 * To run a mutation, you first call `usePostApiPatientInforCheckKohiInfoDifferenceMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPatientInforCheckKohiInfoDifferenceMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPatientInforCheckKohiInfoDifferenceMutation, { data, loading, error }] = usePostApiPatientInforCheckKohiInfoDifferenceMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiPatientInforCheckKohiInfoDifferenceMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPatientInforCheckKohiInfoDifferenceMutation,
    PostApiPatientInforCheckKohiInfoDifferenceMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPatientInforCheckKohiInfoDifferenceMutation,
    PostApiPatientInforCheckKohiInfoDifferenceMutationVariables
  >(PostApiPatientInforCheckKohiInfoDifferenceDocument, options);
}
export type PostApiPatientInforCheckKohiInfoDifferenceMutationHookResult =
  ReturnType<typeof usePostApiPatientInforCheckKohiInfoDifferenceMutation>;
export type PostApiPatientInforCheckKohiInfoDifferenceMutationResult =
  Apollo.MutationResult<PostApiPatientInforCheckKohiInfoDifferenceMutation>;
export type PostApiPatientInforCheckKohiInfoDifferenceMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPatientInforCheckKohiInfoDifferenceMutation,
    PostApiPatientInforCheckKohiInfoDifferenceMutationVariables
  >;
export const PostApiPatientInforCheckPmhKohiInfoDifferenceDocument = gql`
  mutation postApiPatientInforCheckPmhKohiInfoDifference(
    $payload: EmrCloudApiRequestsPatientInforCheckPmhKohiInfoDifferenceRequestInput
  ) {
    postApiPatientInforCheckPmhKohiInfoDifference(
      emrCloudApiRequestsPatientInforCheckPmhKohiInfoDifferenceRequestInput: $payload
    ) {
      message
      status
      data {
        checkPmhKohiInfoDifferenceDto {
          pmhKohiInfoDifferenceProperties {
            index
            isMap
            kohiValue
            name
            xmlValue
          }
          hokenId
          hokenName
          isContinue
          isNotExistKohiCompare
          isMapAll
          seqNo
        }
      }
    }
  }
`;
export type PostApiPatientInforCheckPmhKohiInfoDifferenceMutationFn =
  Apollo.MutationFunction<
    PostApiPatientInforCheckPmhKohiInfoDifferenceMutation,
    PostApiPatientInforCheckPmhKohiInfoDifferenceMutationVariables
  >;

/**
 * __usePostApiPatientInforCheckPmhKohiInfoDifferenceMutation__
 *
 * To run a mutation, you first call `usePostApiPatientInforCheckPmhKohiInfoDifferenceMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPatientInforCheckPmhKohiInfoDifferenceMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPatientInforCheckPmhKohiInfoDifferenceMutation, { data, loading, error }] = usePostApiPatientInforCheckPmhKohiInfoDifferenceMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiPatientInforCheckPmhKohiInfoDifferenceMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPatientInforCheckPmhKohiInfoDifferenceMutation,
    PostApiPatientInforCheckPmhKohiInfoDifferenceMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPatientInforCheckPmhKohiInfoDifferenceMutation,
    PostApiPatientInforCheckPmhKohiInfoDifferenceMutationVariables
  >(PostApiPatientInforCheckPmhKohiInfoDifferenceDocument, options);
}
export type PostApiPatientInforCheckPmhKohiInfoDifferenceMutationHookResult =
  ReturnType<typeof usePostApiPatientInforCheckPmhKohiInfoDifferenceMutation>;
export type PostApiPatientInforCheckPmhKohiInfoDifferenceMutationResult =
  Apollo.MutationResult<PostApiPatientInforCheckPmhKohiInfoDifferenceMutation>;
export type PostApiPatientInforCheckPmhKohiInfoDifferenceMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPatientInforCheckPmhKohiInfoDifferenceMutation,
    PostApiPatientInforCheckPmhKohiInfoDifferenceMutationVariables
  >;
export const PostApiPatientInforSaveOnlineMyCardBeforeReceptionDocument = gql`
  mutation postApiPatientInforSaveOnlineMyCardBeforeReception(
    $payload: EmrCloudApiRequestsPatientInforSaveOnlineMyCardBeforeReceptionRequestInput
  ) {
    postApiPatientInforSaveOnlineMyCardBeforeReception(
      emrCloudApiRequestsPatientInforSaveOnlineMyCardBeforeReceptionRequestInput: $payload
    ) {
      data {
        hokenIds
        kohiIds
        status
      }
    }
  }
`;
export type PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutationFn =
  Apollo.MutationFunction<
    PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutation,
    PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutationVariables
  >;

/**
 * __usePostApiPatientInforSaveOnlineMyCardBeforeReceptionMutation__
 *
 * To run a mutation, you first call `usePostApiPatientInforSaveOnlineMyCardBeforeReceptionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPatientInforSaveOnlineMyCardBeforeReceptionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPatientInforSaveOnlineMyCardBeforeReceptionMutation, { data, loading, error }] = usePostApiPatientInforSaveOnlineMyCardBeforeReceptionMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiPatientInforSaveOnlineMyCardBeforeReceptionMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutation,
    PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutation,
    PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutationVariables
  >(PostApiPatientInforSaveOnlineMyCardBeforeReceptionDocument, options);
}
export type PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutationHookResult =
  ReturnType<
    typeof usePostApiPatientInforSaveOnlineMyCardBeforeReceptionMutation
  >;
export type PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutationResult =
  Apollo.MutationResult<PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutation>;
export type PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutation,
    PostApiPatientInforSaveOnlineMyCardBeforeReceptionMutationVariables
  >;
