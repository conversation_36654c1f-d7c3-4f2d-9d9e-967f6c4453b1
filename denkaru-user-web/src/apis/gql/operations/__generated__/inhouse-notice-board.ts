import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetWhiteBoardQueryVariables = Types.Exact<{ [key: string]: never }>;

export type GetWhiteBoardQuery = {
  __typename?: "query_root";
  getWhiteBoard: string;
};

export type EditWhiteBoardMutationVariables = Types.Exact<{
  contentAfter: Types.Scalars["String"]["input"];
  contentBefore: Types.Scalars["String"]["input"];
}>;

export type EditWhiteBoardMutation = {
  __typename?: "mutation_root";
  editWhiteBoard: boolean;
};

export const GetWhiteBoardDocument = gql`
  query getWhiteBoard {
    getWhiteBoard
  }
`;

/**
 * __useGetWhiteBoardQuery__
 *
 * To run a query within a React component, call `useGetWhiteBoardQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetWhiteBoardQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetWhiteBoardQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetWhiteBoardQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetWhiteBoardQuery,
    GetWhiteBoardQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetWhiteBoardQuery, GetWhiteBoardQueryVariables>(
    GetWhiteBoardDocument,
    options,
  );
}
export function useGetWhiteBoardLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetWhiteBoardQuery,
    GetWhiteBoardQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetWhiteBoardQuery, GetWhiteBoardQueryVariables>(
    GetWhiteBoardDocument,
    options,
  );
}
export function useGetWhiteBoardSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetWhiteBoardQuery,
    GetWhiteBoardQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetWhiteBoardQuery,
    GetWhiteBoardQueryVariables
  >(GetWhiteBoardDocument, options);
}
export type GetWhiteBoardQueryHookResult = ReturnType<
  typeof useGetWhiteBoardQuery
>;
export type GetWhiteBoardLazyQueryHookResult = ReturnType<
  typeof useGetWhiteBoardLazyQuery
>;
export type GetWhiteBoardSuspenseQueryHookResult = ReturnType<
  typeof useGetWhiteBoardSuspenseQuery
>;
export type GetWhiteBoardQueryResult = Apollo.QueryResult<
  GetWhiteBoardQuery,
  GetWhiteBoardQueryVariables
>;
export const EditWhiteBoardDocument = gql`
  mutation editWhiteBoard($contentAfter: String!, $contentBefore: String!) {
    editWhiteBoard(contentAfter: $contentAfter, contentBefore: $contentBefore)
  }
`;
export type EditWhiteBoardMutationFn = Apollo.MutationFunction<
  EditWhiteBoardMutation,
  EditWhiteBoardMutationVariables
>;

/**
 * __useEditWhiteBoardMutation__
 *
 * To run a mutation, you first call `useEditWhiteBoardMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditWhiteBoardMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editWhiteBoardMutation, { data, loading, error }] = useEditWhiteBoardMutation({
 *   variables: {
 *      contentAfter: // value for 'contentAfter'
 *      contentBefore: // value for 'contentBefore'
 *   },
 * });
 */
export function useEditWhiteBoardMutation(
  baseOptions?: Apollo.MutationHookOptions<
    EditWhiteBoardMutation,
    EditWhiteBoardMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    EditWhiteBoardMutation,
    EditWhiteBoardMutationVariables
  >(EditWhiteBoardDocument, options);
}
export type EditWhiteBoardMutationHookResult = ReturnType<
  typeof useEditWhiteBoardMutation
>;
export type EditWhiteBoardMutationResult =
  Apollo.MutationResult<EditWhiteBoardMutation>;
export type EditWhiteBoardMutationOptions = Apollo.BaseMutationOptions<
  EditWhiteBoardMutation,
  EditWhiteBoardMutationVariables
>;
