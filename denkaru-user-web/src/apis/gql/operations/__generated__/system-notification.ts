import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetSystemNotificationsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetSystemNotificationsQuery = {
  __typename?: "query_root";
  getSystemNotifications: Array<{
    __typename?: "SystemNotification";
    systemNotificationID: number;
    tagList: Array<number>;
    title: string;
    description: string;
    createdAt: string;
  }>;
};

export type GetReadSystemNotificationsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetReadSystemNotificationsQuery = {
  __typename?: "query_root";
  getReadSystemNotifications: Array<number>;
};

export type EditReadSystemNotificationsMutationVariables = Types.Exact<{
  input: Array<Types.Scalars["Int"]["input"]> | Types.Scalars["Int"]["input"];
}>;

export type EditReadSystemNotificationsMutation = {
  __typename?: "mutation_root";
  editReadSystemNotifications: boolean;
};

export const GetSystemNotificationsDocument = gql`
  query getSystemNotifications {
    getSystemNotifications {
      systemNotificationID
      tagList
      title
      description
      createdAt
    }
  }
`;

/**
 * __useGetSystemNotificationsQuery__
 *
 * To run a query within a React component, call `useGetSystemNotificationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSystemNotificationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSystemNotificationsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetSystemNotificationsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetSystemNotificationsQuery,
    GetSystemNotificationsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetSystemNotificationsQuery,
    GetSystemNotificationsQueryVariables
  >(GetSystemNotificationsDocument, options);
}
export function useGetSystemNotificationsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSystemNotificationsQuery,
    GetSystemNotificationsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetSystemNotificationsQuery,
    GetSystemNotificationsQueryVariables
  >(GetSystemNotificationsDocument, options);
}
export function useGetSystemNotificationsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSystemNotificationsQuery,
    GetSystemNotificationsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSystemNotificationsQuery,
    GetSystemNotificationsQueryVariables
  >(GetSystemNotificationsDocument, options);
}
export type GetSystemNotificationsQueryHookResult = ReturnType<
  typeof useGetSystemNotificationsQuery
>;
export type GetSystemNotificationsLazyQueryHookResult = ReturnType<
  typeof useGetSystemNotificationsLazyQuery
>;
export type GetSystemNotificationsSuspenseQueryHookResult = ReturnType<
  typeof useGetSystemNotificationsSuspenseQuery
>;
export type GetSystemNotificationsQueryResult = Apollo.QueryResult<
  GetSystemNotificationsQuery,
  GetSystemNotificationsQueryVariables
>;
export const GetReadSystemNotificationsDocument = gql`
  query getReadSystemNotifications {
    getReadSystemNotifications
  }
`;

/**
 * __useGetReadSystemNotificationsQuery__
 *
 * To run a query within a React component, call `useGetReadSystemNotificationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetReadSystemNotificationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetReadSystemNotificationsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetReadSystemNotificationsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetReadSystemNotificationsQuery,
    GetReadSystemNotificationsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetReadSystemNotificationsQuery,
    GetReadSystemNotificationsQueryVariables
  >(GetReadSystemNotificationsDocument, options);
}
export function useGetReadSystemNotificationsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetReadSystemNotificationsQuery,
    GetReadSystemNotificationsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetReadSystemNotificationsQuery,
    GetReadSystemNotificationsQueryVariables
  >(GetReadSystemNotificationsDocument, options);
}
export function useGetReadSystemNotificationsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetReadSystemNotificationsQuery,
    GetReadSystemNotificationsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetReadSystemNotificationsQuery,
    GetReadSystemNotificationsQueryVariables
  >(GetReadSystemNotificationsDocument, options);
}
export type GetReadSystemNotificationsQueryHookResult = ReturnType<
  typeof useGetReadSystemNotificationsQuery
>;
export type GetReadSystemNotificationsLazyQueryHookResult = ReturnType<
  typeof useGetReadSystemNotificationsLazyQuery
>;
export type GetReadSystemNotificationsSuspenseQueryHookResult = ReturnType<
  typeof useGetReadSystemNotificationsSuspenseQuery
>;
export type GetReadSystemNotificationsQueryResult = Apollo.QueryResult<
  GetReadSystemNotificationsQuery,
  GetReadSystemNotificationsQueryVariables
>;
export const EditReadSystemNotificationsDocument = gql`
  mutation editReadSystemNotifications($input: [Int!]!) {
    editReadSystemNotifications(input: $input)
  }
`;
export type EditReadSystemNotificationsMutationFn = Apollo.MutationFunction<
  EditReadSystemNotificationsMutation,
  EditReadSystemNotificationsMutationVariables
>;

/**
 * __useEditReadSystemNotificationsMutation__
 *
 * To run a mutation, you first call `useEditReadSystemNotificationsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditReadSystemNotificationsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editReadSystemNotificationsMutation, { data, loading, error }] = useEditReadSystemNotificationsMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useEditReadSystemNotificationsMutation(
  baseOptions?: Apollo.MutationHookOptions<
    EditReadSystemNotificationsMutation,
    EditReadSystemNotificationsMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    EditReadSystemNotificationsMutation,
    EditReadSystemNotificationsMutationVariables
  >(EditReadSystemNotificationsDocument, options);
}
export type EditReadSystemNotificationsMutationHookResult = ReturnType<
  typeof useEditReadSystemNotificationsMutation
>;
export type EditReadSystemNotificationsMutationResult =
  Apollo.MutationResult<EditReadSystemNotificationsMutation>;
export type EditReadSystemNotificationsMutationOptions =
  Apollo.BaseMutationOptions<
    EditReadSystemNotificationsMutation,
    EditReadSystemNotificationsMutationVariables
  >;
