import { createContext, useContext } from "react";

import { ContentLoading } from "@/components/ui/ContentLoading";
import { useGetPortalHospital } from "@/features/setting-portal/hooks/useGetPortalHospital";
import { usePublicHospitalInfo } from "@/features/setting-portal/hooks/usePublicHospitalInfo";

import type { GetExaminationsQuery } from "@/apis/gql/operations/__generated__/examination";
import type { GetPublicHospitalQuery } from "@/apis/gql/operations/__generated__/hospital";
import type { GetPortalHospitalQuery } from "@/apis/gql/operations/__generated__/portal-hospital";
import type { GetSpecialistsQuery } from "@/apis/gql/operations/__generated__/specialist";
import type { GetTagsQuery } from "@/apis/gql/operations/__generated__/tag";
import type { ReactNode } from "react";

type PortalContextType = {
  hospital: NonNullable<
    GetPortalHospitalQuery["getPortalHospitalById"]["portalHospital"]
  >;
  tags: NonNullable<GetTagsQuery["getTags"]>;
  examination: NonNullable<GetExaminationsQuery["getExaminations"]>;
  specialist: NonNullable<GetSpecialistsQuery["getSpecialists"]>;
  hospitalInfo: NonNullable<GetPublicHospitalQuery["getPublicHospital"]>;
};

const PortalContext = createContext<PortalContextType | undefined>(undefined);

export const usePortal = () => {
  const context = useContext(PortalContext);
  if (context === undefined) {
    throw new Error("usePortal must be used within a PortalProvider");
  }
  return context;
};

type PortalProviderProps = {
  children: ReactNode;
};

export const PortalProvider = ({ children }: PortalProviderProps) => {
  const { loading, hospital, tags, examination, specialist, hasError } =
    useGetPortalHospital();

  const { hospital: hospitalInfo, loading: hospitalInfoLoading } =
    usePublicHospitalInfo({
      skip: loading,
    });

  if (loading || hospitalInfoLoading) {
    return <ContentLoading />;
  }

  if (
    hasError ||
    !tags ||
    !examination ||
    !specialist ||
    !hospital ||
    !hospitalInfo
  ) {
    return null;
  }

  const value: PortalContextType = {
    hospital,
    tags,
    examination,
    specialist,
    hospitalInfo,
  };

  return (
    <PortalContext.Provider value={value}>{children}</PortalContext.Provider>
  );
};
