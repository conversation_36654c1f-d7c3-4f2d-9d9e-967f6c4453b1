import type {
  InsuranceCardFormType,
  RegistrationPublicExpenseInforForm,
} from "./insurance";
import type { FetchResult } from "@apollo/client";
import type { Dayjs } from "dayjs";
import type { PostApiPatientInforSavePatientMutation } from "@/apis/gql/operations/__generated__/patient-infor";
import type {
  DomainModelsInsuranceAiChatHokenCheckDto,
  DomainModelsInsuranceEndDateModelInput,
  DomainModelsMaxMoneyLimitListModel,
  DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation,
  EmrCloudApiRequestsOnlineOnlineViewResultUpdateConfirmRequestInput,
  EmrCloudApiRequestsPatientInforBasicPatientInfoSaveInsuranceInfoRequestInput,
  EmrCloudApiRequestsPatientInforSaveOnlineMyCardBeforeReceptionRequestInput,
  EmrCloudApiResponsesOnlineConvertXmlToQcXmlMsgResponse,
  EmrCloudApiResponsesOnlineDtoOnlineConfirmationHistoryDto,
  Patient,
  SystemNotification,
} from "@/apis/gql/generated/types";

export type PatientSexType = "M" | "F";

export type PatientInfoFormType = {
  onlineCertification: {
    birthdate?: Dayjs;
    hokensyaNo: string;
    kigo: string;
    bango: string;
    edaNo: string;
    qualificationConfirmationDate?: Dayjs;
    limitConsFlg: "0" | "1";
  };
  ptInf: {
    ptNum: string;
    isTester: string;
    name: string;
    kanaName: string;
    birthday?: Dayjs;
    sex: number;
    homePost: string;
    homeAddress1: string;
    homeAddress2: string;
    tel1: string;
    tel2: string;
    mail: string;
    job: string;
    setanusi: string;
    zokugara: string;
    deathDate?: Dayjs;
    ptMemo: string;
    isSaveDuplicateInfo: boolean;
  };
};

export type PatientInfoRefProps = {
  handleSavePatientInfo: (
    type: "OPEN_HISTORY" | "QUIT" | "NEXT",
  ) => Promise<FetchResult<PostApiPatientInforSavePatientMutation>>;
  getPatientInfo: () => PatientInfoFormType["ptInf"];
  resultOfQualificationConfirmation: DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation[];
  onlineXmlString: string;
  checkDate: () => Dayjs | undefined;
};

export type DataTableMaxMoneyItem = DomainModelsMaxMoneyLimitListModel & {
  key: string;
  isModify: boolean;
  hasError: boolean;
};

type AddPatientProps = {
  initialStep?: "CREATE_PATIENT" | "CREATE_RECEPTION" | "ONLY_ADD_PATIENT";
  patientId?: string;
  raiinNo?: string;
  onCreatedReception?: (raiinNo: string) => void;
  isEdit?: boolean;
  onlineConfirmationHistoryId?: string;
  from?: "PATIENT_SEARCH" | "RECEPTION_DRAWER";
};

export type EndDateModels = Partial<
  DomainModelsInsuranceEndDateModelInput & {
    pending: boolean;
    name: string;
    startDate?: number;
  }
>;

type OnlineMasterData = Omit<
  EmrCloudApiRequestsPatientInforSaveOnlineMyCardBeforeReceptionRequestInput,
  "endDateModels"
> & {
  endDateModels?: Array<EndDateModels>;
};

export type PatientState = {
  modal: {
    // Patient
    reservationHistoryOpen: boolean;
    reservationOpen: boolean;
    newPatientOpen: boolean;

    // Online
    credentialsOpen: boolean;
    compareHokenOpen: boolean;
    compareMaruchoOpen: boolean;
    compareIdentityOpen: boolean;
    maidenNameOpen: boolean;
    comparePmhOpen: boolean;

    //Insurance
    handleInsuranceCardOpen: boolean;
    deleteInsuranceCardOpen: boolean;
    outcomeOpen: boolean;
    deleteOutcomeOpen: boolean;
    benefitTypeOpen: boolean;
    deleteBenefitTypeOpen: boolean;
    handleAutomobileInsuranceOpen: boolean;
    deleteAutomobileInsuranceOpen: boolean;
    handleWorkRelatedInjuryOpen: boolean;
    deleteWorkRelatedInjuryOpen: boolean;
    insuranceSelectionOpen: boolean;
    qualificationConfirmationHistoryOpen: boolean;
    onlineQualificationVerificationOpen: boolean;
    handlePublicExpense: boolean;
    deletePublicExpenseOpen: boolean;
    uploadFileOpen: boolean;
    publicExpenseCostOpen: boolean;
    changeExpirationDateOpen: boolean;
  };
  duplicateModal: {
    insuranceCard: Record<number, Partial<InsuranceCardFormType>>;
    publicExpense: Record<number, Partial<RegistrationPublicExpenseInforForm>>;
  };
  patientProps: AddPatientProps | null;
  createdPatientId: number | null;
  selectedPatient: Patient | null;
  listHokenCheck: DomainModelsInsuranceAiChatHokenCheckDto[] | null;
  patientReservationProps?: {
    treatmentDepartmentId?: number;
  };

  // Insurance
  selectedInsurance:
    | (Partial<InsuranceCardFormType & RegistrationPublicExpenseInforForm> & {
        hokenId: number;
        seqNo: string;
        isPmh?: boolean;
        isMarucho?: boolean;
        isLastNodePmh?: boolean;
      })
    | null;
  selectedSinDate: number;

  onlineMasterData: OnlineMasterData | null;
  onlineInsuranceData:
    | (Pick<
        EmrCloudApiRequestsPatientInforBasicPatientInfoSaveInsuranceInfoRequestInput,
        | "isConfirmOnline"
        | "onlineConfirmationHistory"
        | "patientInfo"
        | "endDateModel"
        | "ptKyuseiModel"
      > & {
        endDateModel?: {
          name?: string;
          startDate?: number;
        };
      })
    | null;
  onlineViewResultData:
    | (EmrCloudApiRequestsOnlineOnlineViewResultUpdateConfirmRequestInput & {
        checkDate?: string;
        resultOfQualification?: DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation[];
      })
    | null;
  confirmingType:
    | "ADDING_PATIENT_MY_CARD"
    | "ADDING_RECEPTION_MY_CARD"
    | "ADDING_PATIENT_MY_INSURANCE"
    | "CONFIRMING_HOKEN_MY_INSURANCE"
    | "CONFIRMING_KOHI_MY_INSURANCE"
    | "ADDING_HOKEN_MY_INSURANCE"
    | "ADDING_KOHI_MY_INSURANCE"
    | "EDITING_HOKEN_MY_INSURANCE"
    | "EDITING_KOHI_MY_INSURANCE"
    | "VIEW_RESULT_MY_CARD"
    | "VIEW_RESULT_MY_CARD_ADD_PATIENT"
    | null;
  callbackList: Array<
    (payload?: PatientState["onlineMasterData"] | VoidFunction) => void
  >;
  onlineConfirmHistoryData:
    | EmrCloudApiResponsesOnlineConvertXmlToQcXmlMsgResponse
    | EmrCloudApiResponsesOnlineDtoOnlineConfirmationHistoryDto
    | null;
  systemNotifications: SystemNotification[] | null;
  readSystemNotifications: number[] | null;
  refetchReadSystemNotifications: () => void;
};

export type PatientContextType = PatientState & {
  handleOpenModal: (payload: PatientModalType) => void;
  handleCloseModal: (payload: PatientModalType) => void;
  handleCloseAllModal: () => void;

  handleOpenDuplicateInsuranceCardModal: (
    payload: PatientState["duplicateModal"]["insuranceCard"],
  ) => void;
  handleOpenDuplicatePublicExpenseModal: (
    payload: PatientState["duplicateModal"]["publicExpense"],
  ) => void;
  handleCloseDuplicateInsuranceCardModal: (payload: number) => void;
  handleCloseDuplicatePublicExpenseModal: (payload: number) => void;

  handleSetPatient: (patient: PatientState["selectedPatient"]) => void;
  handleSetPatientProps: (payload: PatientState["patientProps"]) => void;
  handleSetPatientReservationProps: (
    payload: PatientState["patientReservationProps"],
  ) => void;

  setSelectedInsurance: (payload: PatientState["selectedInsurance"]) => void;
  handleSetSelectedSinDate: (payload: PatientState["selectedSinDate"]) => void;

  resetAllData: () => void;
  resetOnlineData: () => void;
  handleUpdateOnlineMasterData: (
    payload: PatientState["onlineMasterData"],
  ) => void;
  handleUpdateOnlineInsuranceData: (
    payload: PatientState["onlineInsuranceData"],
  ) => void;
  handleUpdateOnlineViewResultData: (
    payload: PatientState["onlineViewResultData"],
  ) => void;
  changeConfirmingType: (payload: PatientState["confirmingType"]) => void;
  handleSetCallback: (payload: PatientState["callbackList"]) => void;
  handleUpdateCallback: (payload: PatientState["callbackList"]) => void;
  handleSetOnlineConfirmationHistoryData: (
    payload: PatientState["onlineConfirmHistoryData"],
  ) => void;
  handleSetListHokenCheck: (payload: PatientState["listHokenCheck"]) => void;
  handleSetSystemNotifications: (
    payload: PatientState["systemNotifications"],
  ) => void;
  handleSetReadSystemNotifications: (
    payload: PatientState["readSystemNotifications"],
  ) => void;
  handleSetRefetchReadSystemNotifications: (
    payload: PatientState["refetchReadSystemNotifications"],
  ) => void;
  handleSetCreatedPatientId: (
    payload: PatientState["createdPatientId"],
  ) => void;
};

export type PatientAction =
  | {
      type: "OPEN_MODAL" | "CLOSE_MODAL";
      payload: PatientModalType;
    }
  | {
      type: "CLOSE_ALL_MODAL";
    }
  | {
      type: "SET_PATIENT";
      payload: PatientState["selectedPatient"];
    }
  | {
      type: "SET_PATIENT_PROPS";
      payload: PatientState["patientProps"];
    }
  | {
      type: "UPDATE_ONLINE_MASTER";
      payload: PatientState["onlineMasterData"];
    }
  | {
      type: "UPDATE_ONLINE_INSURANCE";
      payload: PatientState["onlineInsuranceData"];
    }
  | {
      type: "UPDATE_ONLINE_VIEW_RESULT";
      payload: PatientState["onlineViewResultData"];
    }
  | {
      type: "CHANGE_CONFIRMING_TYPE";
      payload: PatientState["confirmingType"];
    }
  | {
      type: "SET_SELECTED_INSURANCE";
      payload: PatientState["selectedInsurance"];
    }
  | {
      type: "SET_SELECTED_SIN_DATE";
      payload: PatientState["selectedSinDate"];
    }
  | {
      type: "SET_CALLBACK";
      payload: PatientState["callbackList"];
    }
  | {
      type: "UPDATE_CALLBACK";
      payload: PatientState["callbackList"];
    }
  | {
      type: "OPEN_DUPLICATE_INSURANCE_CARD";
      payload: PatientState["duplicateModal"]["insuranceCard"];
    }
  | {
      type: "OPEN_DUPLICATE_PUBLIC_EXPENSE";
      payload: PatientState["duplicateModal"]["publicExpense"];
    }
  | {
      type: "CLOSE_DUPLICATE_INSURANCE_CARD";
      payload: number;
    }
  | {
      type: "CLOSE_DUPLICATE_PUBLIC_EXPENSE";
      payload: number;
    }
  | {
      type: "RESET_ONLINE_DATA";
    }
  | {
      type: "SET_ONLINE_HISTORY";
      payload: PatientState["onlineConfirmHistoryData"];
    }
  | {
      type: "SET_LIST_HOKEN_CHECK";
      payload: PatientState["listHokenCheck"];
    }
  | {
      type: "SET_RESERVATION_PROPS";
      payload: PatientState["patientReservationProps"];
    }
  | {
      type: "SET_SYSTEM_NOTIFICATIONS";
      payload: PatientState["systemNotifications"];
    }
  | {
      type: "SET_READ_SYSTEM_NOTIFICATIONS";
      payload: PatientState["readSystemNotifications"];
    }
  | {
      type: "SET_REFETCH_READ_SYSTEM_NOTIFICATIONS";
      payload: PatientState["refetchReadSystemNotifications"];
    }
  | {
      type: "SET_CREATED_PATIENT";
      payload: PatientState["createdPatientId"];
    };

type CompareModal<T> = {
  isMap?: boolean;
  value?: T;
  xmlValue?: T;
};

export type ComparePatientState = {
  name: CompareModal<string>;
  kanaName: CompareModal<string>;
  birthday: CompareModal<number>;
  sex: CompareModal<number>;
  homePost: CompareModal<string>;
  address: CompareModal<string>;
  setanusi: CompareModal<string>;
  isMapAll: boolean;
};

export type PatientModalType =
  | "RESERVATION_HISTORY"
  | "RESERVATION"
  | "NEW_PATIENT"
  | "CREDENTIALS"
  | "COMPARE_HOKEN"
  | "COMPARE_MARUCHO"
  | "COMPARE_IDENTITY"
  | "COMPARE_PMH"
  | "MAIDEN_NAME"
  | "HANDLE_INSURANCE_CARD"
  | "DELETE_INSURANCE_CARD"
  | "OUTCOME"
  | "DELETE_OUTCOME"
  | "BENEFIT_TYPE"
  | "DELETE_BENEFIT_TYPE"
  | "HANDLE_AUTOMOBILE_INSURANCE"
  | "DELETE_AUTOMOBILE_INSURANCE"
  | "HANDLE_WORK_RELATED_INJURY"
  | "DELETE_WORK_RELATED_INJURY"
  | "INSURANCE_SELECTION"
  | "QUALIFICATION_CONFIRMATION_HISTORY"
  | "ONLINE_QUALIFICATION_VERIFICATION"
  | "HANDLE_PUBLIC_EXPENSE"
  | "DELETE_PUBLIC_EXPENSE"
  | "UPLOAD_FILE"
  | "PUBLIC_EXPENSE_MAX_COST"
  | "CHANGE_EXPIRATION_DATE";

export type OnlineConfirmationResults = {
  xmlString: string;
  resultOfQualification: DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation[];
};
