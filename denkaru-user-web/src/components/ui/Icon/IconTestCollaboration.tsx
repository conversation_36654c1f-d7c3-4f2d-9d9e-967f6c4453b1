// ![DO NOT EDIT] this file is auto-generated by svgr;
import * as React from "react";
import type { SVGProps } from "react";
export const SvgIconTestCollaboration = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <path
      fill="#005BAC"
      fillRule="evenodd"
      d="M4.175 15.825Q5.35 17 7 17h2v-2H7q-.824 0-1.412-.588A1.93 1.93 0 0 1 5 13q0-.825.588-1.412A1.93 1.93 0 0 1 7 11h1.5q1.45 0 2.475-1.025A3.37 3.37 0 0 0 12 7.5q0-1.45-1.025-2.475A3.37 3.37 0 0 0 8.5 4H5a.97.97 0 0 0-.713.287A.97.97 0 0 0 4 5q0 .424.287.713Q4.575 6 5 6h3.5q.624 0 1.063.438Q10 6.875 10 7.5q0 .624-.438 1.063A1.45 1.45 0 0 1 8.5 9H7q-1.65 0-2.825 1.175T3 13c0 1.65.392 2.042 1.175 2.825M14 20q-.825 0-1.412-.587A1.93 1.93 0 0 1 12 18h-2v-4h2q0-.825.588-1.412A1.93 1.93 0 0 1 14 12h3v8zm4-5h2a.97.97 0 0 0 .712-.287A.97.97 0 0 0 21 14a.97.97 0 0 0-.288-.713A.97.97 0 0 0 20 13h-2zm0 4h2q.424 0 .712-.288A.97.97 0 0 0 21 18a.97.97 0 0 0-.288-.712A.97.97 0 0 0 20 17h-2z"
      clipRule="evenodd"
    />
  </svg>
);
