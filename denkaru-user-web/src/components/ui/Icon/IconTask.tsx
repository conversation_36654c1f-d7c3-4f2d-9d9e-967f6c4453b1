// ![DO NOT EDIT] this file is auto-generated by svgr;
import * as React from "react";
import type { SVGProps } from "react";
export const SvgIconTask = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <path
      fill="#005BAC"
      d="M11.125 16a1 1 0 0 1 1-1H18a1 1 0 1 1 0 2h-5.875a1 1 0 0 1-1-1m1-5c-.621 0-1.125.448-1.125 1s.504 1 1.125 1h5.75c.621 0 1.125-.448 1.125-1s-.504-1-1.125-1zM11 8a1 1 0 0 1 1-1h5.875a1 1 0 1 1 0 2H12a1 1 0 0 1-1-1m-1.805 3.195a.7.7 0 0 0-.99-.99L6.7 11.71l-.505-.505a.7.7 0 0 0-.99.99l1 1a.7.7 0 0 0 .99 0zm0 3.01a.7.7 0 0 1 0 .99l-2 2a.7.7 0 0 1-.99 0l-1-1a.7.7 0 0 1 .99-.99l.505.505 1.505-1.505a.7.7 0 0 1 .99 0m0-7.01a.7.7 0 1 0-.99-.99L6.7 7.71l-.505-.505a.7.7 0 1 0-.99.99l1 1a.7.7 0 0 0 .99 0z"
    />
    <path
      fill="#005BAC"
      fillRule="evenodd"
      d="M5 2a4 4 0 0 0-4 4v12a4 4 0 0 0 4 4h14a4 4 0 0 0 4-4V6a4 4 0 0 0-4-4zm14 2H5a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2"
      clipRule="evenodd"
    />
  </svg>
);
