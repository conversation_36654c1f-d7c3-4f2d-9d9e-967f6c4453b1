// ![DO NOT EDIT] this file is auto-generated by svgr;
import * as React from "react";
import type { SVGProps } from "react";
export const SvgIconTestCollaborationWhite = (
  props: SVGProps<SVGSVGElement>,
) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    {...props}
  >
    <path
      fill="#fff"
      fillRule="evenodd"
      d="M2.175 13.825Q3.35 15 5 15h2v-2H5q-.824 0-1.413-.588A1.93 1.93 0 0 1 3 11q0-.825.587-1.412A1.93 1.93 0 0 1 5 9h1.5q1.45 0 2.475-1.025A3.37 3.37 0 0 0 10 5.5q0-1.45-1.025-2.475A3.37 3.37 0 0 0 6.5 2H3a.97.97 0 0 0-.712.288A.97.97 0 0 0 2 3q0 .424.288.712A.97.97 0 0 0 3 4h3.5q.624 0 1.063.438Q8 4.875 8 5.5q0 .624-.438 1.063A1.45 1.45 0 0 1 6.5 7H5Q3.35 7 2.175 8.175T1 11c0 1.65.392 2.042 1.175 2.825M12 18q-.825 0-1.412-.587A1.93 1.93 0 0 1 10 16H8v-4h2q0-.825.588-1.412A1.93 1.93 0 0 1 12 10h3v8zm4-5h2a.97.97 0 0 0 .712-.287A.97.97 0 0 0 19 12a.97.97 0 0 0-.288-.713A.97.97 0 0 0 18 11h-2zm0 4h2q.424 0 .712-.288A.97.97 0 0 0 19 16a.97.97 0 0 0-.288-.713A.97.97 0 0 0 18 15h-2z"
      clipRule="evenodd"
    />
  </svg>
);
