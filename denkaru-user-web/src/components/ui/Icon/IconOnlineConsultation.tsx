// ![DO NOT EDIT] this file is auto-generated by svgr;
import * as React from "react";
import type { SVGProps } from "react";
export const SvgIconOnlineConsultation = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <path
      fill="#005BAC"
      fillRule="evenodd"
      d="M12 11a3 3 0 1 0 0-6 3 3 0 0 0 0 6m0-1.4a1.6 1.6 0 1 0 0-3.2 1.6 1.6 0 0 0 0 3.2"
      clipRule="evenodd"
    />
    <path
      fill="#005BAC"
      fillRule="evenodd"
      d="M8 22v-2h2v-2H3q-.824 0-1.413-.587A1.93 1.93 0 0 1 1 16V4q0-.824.587-1.413A1.93 1.93 0 0 1 3 2h18q.824 0 1.413.587.586.589.587 1.413v12q0 .824-.587 1.413A1.93 1.93 0 0 1 21 18h-7v2h2v2zm13-6V4H3v12h3.387a5.702 5.702 0 0 1 11.226 0zm-4.817 0a4.302 4.302 0 0 0-8.366 0z"
      clipRule="evenodd"
    />
  </svg>
);
