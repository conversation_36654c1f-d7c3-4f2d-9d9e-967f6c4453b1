import Link from "next/link";
import styled from "styled-components";

import { SvgIconAi } from "@/components/ui/Icon/IconAi";

import { SvgHeaderIconClinicMap } from "../Icon/HeaderIconClinicMap";

const GroupButtonWrapper = styled.div`
  display: flex;
  gap: 8px;
  margin: 0 8px;
`;

const StyledLink = styled(Link)`
  display: block;
  text-decoration: none;
  width: 96px;
  height: 40px;
  flex-grow: 0;
  border-radius: 4px;
  border: solid 1px #e2e3e5;
  background-color: #fbfcfe;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.3s;

  &:hover {
    opacity: 0.7;
  }

  &:focus-visible {
    transition: none;
  }
`;

const IconWrapper = styled.div`
  height: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const AIChatLabel = styled.span`
  font-size: 11px;
  color: #243544;
`;

const ClinicMapLabel = styled.span`
  font-family: NotoSansJP;
  font-size: 11px;
  color: #243544;
  letter-spacing: -1px;
`;

type Props = {
  type: "CLINIC" | "PHARMACY";
};

export const GroupButton: React.FC<Props> = ({ type }) => {
  return (
    <GroupButtonWrapper>
      <StyledLink
        href={type === "PHARMACY" ? "/pharmacy/ai-assist" : "/ai-assist"}
        target="_blank"
        rel="noopener noreferrer"
      >
        <IconWrapper>
          <SvgHeaderIconClinicMap />
          <ClinicMapLabel>クリニック・マップ</ClinicMapLabel>
        </IconWrapper>
      </StyledLink>
      <StyledLink href="/reception">
        <IconWrapper>
          <SvgIconAi />
          <AIChatLabel>AIチャート</AIChatLabel>
        </IconWrapper>
      </StyledLink>
    </GroupButtonWrapper>
  );
};
