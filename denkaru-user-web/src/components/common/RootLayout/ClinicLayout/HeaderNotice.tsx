import { useMemo, useState } from "react";

import styled from "styled-components";

import { SvgIconAlert } from "@/components/ui/Icon/IconAlert";
import { SvgIconDotNotification } from "@/components/ui/Icon/IconDotNotification";
import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";
// eslint-disable-next-line import/no-restricted-paths
import { NotificationModal } from "@/features/start/ui/right-content/NotificationModal";

const Wrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--global-header-height);
  width: 55px;
  border-left: 1px solid #034888;
`;

const NoticeContainer = styled.button`
  position: relative;
  background: none;
  border: none;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
`;

const StyledIconAlert = styled(SvgIconAlert)`
  margin-top: 5px;
  width: 24px;
  height: 24px;
`;

const StyledIconDotNotification = styled(SvgIconDotNotification)`
  position: absolute;
  top: 7px;
  right: 2px;
`;

export const HeaderNotice: React.FC<{ hasUnreadNotice?: boolean }> = ({
  hasUnreadNotice = false,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const {
    systemNotifications,
    readSystemNotifications,
    refetchReadSystemNotifications,
  } = usePatientContext();

  const formattedSystemNotifications = useMemo(() => {
    if (!systemNotifications || !readSystemNotifications) {
      return [];
    }
    const readIds = new Set(readSystemNotifications);
    return systemNotifications.map((notification) => ({
      ...notification,
      isRead: readIds.has(notification.systemNotificationID),
    }));
  }, [systemNotifications, readSystemNotifications]);

  const handleNoticeClick = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    if (refetchReadSystemNotifications) {
      refetchReadSystemNotifications();
    }
  };

  return (
    <Wrapper>
      <NoticeContainer onClick={handleNoticeClick}>
        <StyledIconAlert />
        {hasUnreadNotice && <StyledIconDotNotification />}
      </NoticeContainer>
      <NotificationModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        notifications={formattedSystemNotifications}
        refetch={refetchReadSystemNotifications}
      />
    </Wrapper>
  );
};
