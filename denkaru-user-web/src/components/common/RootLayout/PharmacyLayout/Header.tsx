import { useState } from "react";

import { useRouter } from "next/router";
import styled, { css } from "styled-components";
import Link from "next/link";

import { Header as CommonHeader } from "@/components/ui/Header";
import { SvgIconArrowPulldownWhite } from "@/components/ui/Icon/IconArrowPulldownWhite";
import { HeaderMenuItemKey, ZIndexOrder } from "@/constants/common";
import { pharmacyHeaderMenuData } from "@/constants/url";
import { useSession } from "@/hooks/useSession";
import { HeaderSettingButton } from "@/components/ui/HeaderSettingButton";
import { DropdownMenu } from "@/components/ui/DropdownMenu";
import { SvgHeaderIconClinicMap } from "@/components/ui/Icon/HeaderIconClinicMap";

import { PasswordResetModal } from "../PasswordResetModal";

const StyledMenu = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: ${ZIndexOrder.HeaderMenu};
  gap: 8px;
`;

const RightMenu = styled.div`
  display: flex;
  align-items: center;
  margin-left: auto;
`;

const StyledMenuItem = styled.button<{ $active: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  border: none;
  appearance: none;
  cursor: pointer;
  border-radius: 4px;
  height: 32px;
  background-color: #005bac;
  transition: 0.3s;
  white-space: nowrap;

  span {
    font-size: 14px;
    line-height: 1px;
    text-align: center;
    color: #ffffff;
  }

  ${({ $active }) => {
    if ($active) {
      return css`
        background-color: rgba(0, 0, 0, 0.3);
      `;
    }

    return css`
      &:hover,
      &:focus-visible {
        background-color: rgba(0, 0, 0, 0.2);
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
      }
    `;
  }}
`;

const DropdownButton = styled.button`
  min-width: 160px;
  height: var(--global-header-height);
  width: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  appearance: none;
  border: none;
  background-color: #005bac;

  &:focus-visible {
    transition: none;
    outline-offset: -4px;
  }
`;

const StaffName = styled.span`
  font-size: 14px;
  line-height: 14px;
  text-align: center;
  color: #ffffff;
  flex: 1;
`;

const MenuItemLabel = styled.span`
  height: 14px;
  flex-grow: 1;

  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-align: left;
  color: #243544;
`;

const LogoutLabel = styled(MenuItemLabel)`
  color: #e74c3c;
`;

const ArrowIconWrapper = styled.div`
  flex: none;
  margin-top: 7px;
  cursor: pointer;
`;

const StyledLink = styled(Link)`
  display: block;
  text-decoration: none;
  width: 96px;
  height: 40px;
  flex-grow: 0;
  border-radius: 4px;
  border: solid 1px #e2e3e5;
  background-color: #fbfcfe;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.3s;

  &:hover {
    opacity: 0.7;
  }

  &:focus-visible {
    transition: none;
  }
`;

const IconWrapper = styled.div`
  width: 54px;
  height: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const AIAssistLabel = styled.div`
  width: 54px;
  font-size: 11px;
  line-height: 1;
  text-align: center;
  color: #243544;
`;

export const Header: React.FC = () => {
  const router = useRouter();
  const {
    session: { staffInfo, isOperator },
  } = useSession();

  const [isOpenPasswordResetModal, setIsOpenPasswordResetModal] =
    useState(false);

  return (
    <CommonHeader>
      <RightMenu>
        <StyledMenu>
          {pharmacyHeaderMenuData.map((menu) => (
            <StyledMenuItem
              key={menu.pathname}
              $active={[menu.pathname, ...menu.relatedPaths].includes(
                router.pathname,
              )}
              onClick={() =>
                router.push(`${menu.pathname}/${menu.query ?? ""}`)
              }
            >
              <span>{menu.label}</span>
            </StyledMenuItem>
          ))}
        </StyledMenu>

        <StyledLink
          href={"/pharmacy/ai-assist"}
          target="_blank"
          rel="noopener noreferrer"
        >
          <IconWrapper>
            <SvgHeaderIconClinicMap />
            <AIAssistLabel>AIアシスト</AIAssistLabel>
          </IconWrapper>
        </StyledLink>

        <HeaderSettingButton
          href="/pharmacy/setting/account"
          active={router.asPath.startsWith("/pharmacy/setting/")}
        />

        <DropdownMenu
          menu={{
            items: [
              {
                key: HeaderMenuItemKey.PasswordChange,
                label: <MenuItemLabel>パスワード変更</MenuItemLabel>,
                onClick: () => setIsOpenPasswordResetModal(true),
              },
              {
                key: HeaderMenuItemKey.Logout,
                label: <LogoutLabel>ログアウト</LogoutLabel>,
                onClick: () => router.push("/logout"),
              },
            ],
          }}
          trigger={["click"]}
          align={{ offset: [0, 0] }}
        >
          <DropdownButton>
            <StaffName>
              {isOperator
                ? "ヘルステック担当者"
                : `${staffInfo?.staffName || ""}`}
            </StaffName>
            <ArrowIconWrapper>
              <SvgIconArrowPulldownWhite />
            </ArrowIconWrapper>
          </DropdownButton>
        </DropdownMenu>
      </RightMenu>

      <PasswordResetModal
        isOpen={isOpenPasswordResetModal}
        onClose={() => setIsOpenPasswordResetModal(false)}
      />
    </CommonHeader>
  );
};
