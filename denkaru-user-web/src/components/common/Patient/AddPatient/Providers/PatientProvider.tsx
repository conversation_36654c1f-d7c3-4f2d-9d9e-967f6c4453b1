import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useReducer,
} from "react";

import dayjs from "dayjs";
import { useRouter } from "next/router";

import { RenderIf } from "@/utils/common/render-if";
import { PatientReservationHistoryModal } from "@/components/common/PatientSearch/PatientReservationHistoryModal/PatientReservationHistoryModal";
import { PatientReservationModal } from "@/components/common/PatientSearch/PatientReservationModal/PatientReservationModal";
import { STORAGE_KEYS } from "@/constants/local-storage";
import { toDateNumber } from "@/utils/add-patient";
import { getLocalStorage } from "@/utils/local-storage";

import { AutomobileInsuranceModal } from "../AutomobileInsuranceModal";
import { InsuranceCardModal } from "../InsuranceCardModal/InsuranceCardModal";
import { InsuranceSelectionModal } from "../InsuranceSelectionModal";
import { WorkRelatedInjuryModal } from "../WorkRelatedInjuryModal";
import { HandleKohiModal } from "../HandleKohiModal";
import { NewPatientModal } from "../NewPatientModal/NewPatientModal";
import { HandleAddReceptionMyCard } from "../ConfirmOnlineFlowModal/HandleAddReceptionMyCard";
import { HandleMyInsuranceCard } from "../ConfirmOnlineFlowModal/HandleMyInsuranceCard";
import { OnlineQualificationVertificationModal } from "../OnlineQualificationVertificationHistoryModal";
import { QualificationConfirmationHistoryModal } from "../QualificationConfirmationHistoryModal";

import type {
  PatientAction,
  PatientContextType,
  PatientModalType,
  PatientState,
} from "@/types/patient";
import type { PropsWithChildren } from "react";

const initState: PatientState = {
  modal: {
    reservationHistoryOpen: false,
    reservationOpen: false,
    newPatientOpen: false,

    credentialsOpen: false,
    compareHokenOpen: false,
    compareIdentityOpen: false,
    maidenNameOpen: false,
    compareMaruchoOpen: false,
    comparePmhOpen: false,

    handleInsuranceCardOpen: false,
    deleteInsuranceCardOpen: false,
    outcomeOpen: false,
    deleteOutcomeOpen: false,
    benefitTypeOpen: false,
    deleteBenefitTypeOpen: false,
    handleAutomobileInsuranceOpen: false,
    deleteAutomobileInsuranceOpen: false,
    handleWorkRelatedInjuryOpen: false,
    deleteWorkRelatedInjuryOpen: false,
    insuranceSelectionOpen: false,
    qualificationConfirmationHistoryOpen: false,
    onlineQualificationVerificationOpen: false,
    handlePublicExpense: false,
    deletePublicExpenseOpen: false,
    uploadFileOpen: false,
    publicExpenseCostOpen: false,
    changeExpirationDateOpen: false,
  },
  duplicateModal: {
    insuranceCard: {},
    publicExpense: {},
  },

  selectedPatient: null,
  patientProps: null,
  createdPatientId: null,

  selectedInsurance: null,
  selectedSinDate: 0,
  listHokenCheck: null,

  onlineMasterData: null,
  confirmingType: null,
  callbackList: [],
  onlineInsuranceData: null,
  onlineConfirmHistoryData: null,
  onlineViewResultData: null,
  systemNotifications: null,
  readSystemNotifications: null,
  refetchReadSystemNotifications: () => {},
};

const MODAL_MAP: Record<PatientModalType, keyof PatientState["modal"]> = {
  RESERVATION_HISTORY: "reservationHistoryOpen",
  RESERVATION: "reservationOpen",
  NEW_PATIENT: "newPatientOpen",
  CREDENTIALS: "credentialsOpen",
  COMPARE_HOKEN: "compareHokenOpen",
  COMPARE_MARUCHO: "compareMaruchoOpen",
  COMPARE_IDENTITY: "compareIdentityOpen",
  COMPARE_PMH: "comparePmhOpen",
  MAIDEN_NAME: "maidenNameOpen",
  HANDLE_INSURANCE_CARD: "handleInsuranceCardOpen",
  DELETE_INSURANCE_CARD: "deleteInsuranceCardOpen",
  OUTCOME: "outcomeOpen",
  DELETE_OUTCOME: "deleteOutcomeOpen",
  BENEFIT_TYPE: "benefitTypeOpen",
  DELETE_BENEFIT_TYPE: "deleteBenefitTypeOpen",
  HANDLE_AUTOMOBILE_INSURANCE: "handleAutomobileInsuranceOpen",
  DELETE_AUTOMOBILE_INSURANCE: "deleteAutomobileInsuranceOpen",
  HANDLE_WORK_RELATED_INJURY: "handleWorkRelatedInjuryOpen",
  DELETE_WORK_RELATED_INJURY: "deleteWorkRelatedInjuryOpen",
  INSURANCE_SELECTION: "insuranceSelectionOpen",
  QUALIFICATION_CONFIRMATION_HISTORY: "qualificationConfirmationHistoryOpen",
  ONLINE_QUALIFICATION_VERIFICATION: "onlineQualificationVerificationOpen",
  HANDLE_PUBLIC_EXPENSE: "handlePublicExpense",
  DELETE_PUBLIC_EXPENSE: "deletePublicExpenseOpen",
  UPLOAD_FILE: "uploadFileOpen",
  PUBLIC_EXPENSE_MAX_COST: "publicExpenseCostOpen",
  CHANGE_EXPIRATION_DATE: "changeExpirationDateOpen",
};

const PatientContext = createContext<PatientContextType | null>(null);

const reducer = (state: PatientState, action: PatientAction): PatientState => {
  switch (action.type) {
    case "OPEN_MODAL":
    case "CLOSE_MODAL":
      return {
        ...state,
        modal: {
          ...state.modal,
          [MODAL_MAP[action.payload]]: action.type === "OPEN_MODAL",
        },
      };
    case "CLOSE_ALL_MODAL":
      return {
        ...state,
        modal: {
          ...initState.modal,
        },
      };

    case "OPEN_DUPLICATE_INSURANCE_CARD": {
      return {
        ...state,
        duplicateModal: {
          ...state.duplicateModal,
          insuranceCard: {
            ...state.duplicateModal.insuranceCard,
            ...action.payload,
          },
        },
      };
    }

    case "OPEN_DUPLICATE_PUBLIC_EXPENSE": {
      return {
        ...state,
        duplicateModal: {
          ...state.duplicateModal,
          publicExpense: {
            ...state.duplicateModal.publicExpense,
            ...action.payload,
          },
        },
      };
    }

    case "CLOSE_DUPLICATE_INSURANCE_CARD": {
      const newInsuranceCard = { ...state.duplicateModal.insuranceCard };
      delete newInsuranceCard[action.payload];
      return {
        ...state,
        duplicateModal: {
          ...state.duplicateModal,
          insuranceCard: {
            ...newInsuranceCard,
          },
        },
      };
    }

    case "CLOSE_DUPLICATE_PUBLIC_EXPENSE": {
      const newPublicExpense = { ...state.duplicateModal.publicExpense };
      delete newPublicExpense[action.payload];
      return {
        ...state,
        duplicateModal: {
          ...state.duplicateModal,
          publicExpense: {
            ...newPublicExpense,
          },
        },
      };
    }

    case "SET_PATIENT":
      return {
        ...state,
        selectedPatient: action.payload,
      };

    case "SET_PATIENT_PROPS":
      return {
        ...state,
        patientProps: {
          ...action.payload,
        },
      };

    case "SET_RESERVATION_PROPS":
      return {
        ...state,
        patientReservationProps: {
          ...action.payload,
        },
      };

    case "SET_CREATED_PATIENT":
      return {
        ...state,
        createdPatientId: action.payload,
      };

    // Insurance
    case "SET_SELECTED_SIN_DATE":
      return {
        ...state,
        selectedSinDate: action.payload,
      };

    case "SET_SELECTED_INSURANCE": {
      return {
        ...state,
        selectedInsurance: action.payload,
      };
    }

    // Online
    case "UPDATE_ONLINE_MASTER":
      return {
        ...state,
        onlineMasterData: {
          ...state.onlineMasterData,
          ...action.payload,
        },
      };

    case "UPDATE_ONLINE_INSURANCE":
      return {
        ...state,
        onlineInsuranceData: {
          ...state.onlineInsuranceData,
          ...action.payload,
        },
      };

    case "UPDATE_ONLINE_VIEW_RESULT":
      return {
        ...state,
        onlineViewResultData: {
          ...state.onlineViewResultData,
          ...action.payload,
        },
      };

    case "CHANGE_CONFIRMING_TYPE":
      return {
        ...state,
        confirmingType: action.payload,
      };

    case "SET_CALLBACK":
      return {
        ...state,
        callbackList: [...action.payload],
      };

    case "RESET_ONLINE_DATA":
      return {
        ...state,
        onlineInsuranceData: null,
        onlineMasterData: null,
        onlineViewResultData: null,
      };

    case "UPDATE_CALLBACK":
      return {
        ...state,
        callbackList: [...state.callbackList, ...action.payload],
      };

    case "SET_ONLINE_HISTORY":
      return {
        ...state,
        onlineConfirmHistoryData: action.payload,
      };

    case "SET_LIST_HOKEN_CHECK":
      return {
        ...state,
        listHokenCheck: action.payload,
      };

    case "SET_SYSTEM_NOTIFICATIONS":
      return {
        ...state,
        systemNotifications: action.payload,
      };

    case "SET_READ_SYSTEM_NOTIFICATIONS":
      return {
        ...state,
        readSystemNotifications: action.payload,
      };

    case "SET_REFETCH_READ_SYSTEM_NOTIFICATIONS":
      return {
        ...state,
        refetchReadSystemNotifications: action.payload,
      };

    default:
      return {
        ...initState,
        selectedSinDate: Number(
          getLocalStorage<string>(STORAGE_KEYS.INIT_RECEPTION_SIN_DATE) ||
            toDateNumber(dayjs()),
        ),
      };
  }
};

export const PatientProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const router = useRouter();

  const [state, dispatch] = useReducer(reducer, {
    ...initState,
    selectedSinDate: Number(
      getLocalStorage<string>(STORAGE_KEYS.INIT_RECEPTION_SIN_DATE) ||
        toDateNumber(dayjs()),
    ),
  });

  useEffect(() => {
    const handleRouteChange = () => {
      resetAllData();
    };

    router.events.on("routeChangeStart", handleRouteChange);

    return () => {
      router.events.off("routeChangeStart", handleRouteChange);
    };
  }, []);

  // Modal
  const handleOpenModal = useCallback((payload: PatientModalType) => {
    dispatch({ type: "OPEN_MODAL", payload });
  }, []);
  const handleCloseModal = useCallback((payload: PatientModalType) => {
    dispatch({ type: "CLOSE_MODAL", payload });
  }, []);

  const handleCloseAllModal = useCallback(() => {
    dispatch({ type: "CLOSE_ALL_MODAL" });
  }, []);

  const handleOpenDuplicateInsuranceCardModal = useCallback(
    (payload: PatientState["duplicateModal"]["insuranceCard"]) => {
      dispatch({ type: "OPEN_DUPLICATE_INSURANCE_CARD", payload });
    },
    [],
  );

  const handleOpenDuplicatePublicExpenseModal = useCallback(
    (payload: PatientState["duplicateModal"]["publicExpense"]) => {
      dispatch({ type: "OPEN_DUPLICATE_PUBLIC_EXPENSE", payload });
    },
    [],
  );

  const handleCloseDuplicateInsuranceCardModal = useCallback(
    (payload: number) => {
      dispatch({ type: "CLOSE_DUPLICATE_INSURANCE_CARD", payload });
    },
    [],
  );

  const handleCloseDuplicatePublicExpenseModal = useCallback(
    (payload: number) => {
      dispatch({ type: "CLOSE_DUPLICATE_PUBLIC_EXPENSE", payload });
    },
    [],
  );

  // Patient
  const handleSetPatient = useCallback(
    (payload: PatientState["selectedPatient"]) => {
      dispatch({ type: "SET_PATIENT", payload });
    },
    [],
  );

  const handleSetPatientProps = useCallback(
    (payload: PatientState["patientProps"]) => {
      dispatch({ type: "SET_PATIENT_PROPS", payload });
    },
    [],
  );

  const handleSetPatientReservationProps = useCallback(
    (payload: PatientState["patientReservationProps"]) => {
      dispatch({ type: "SET_RESERVATION_PROPS", payload });
    },
    [],
  );

  const handleSetCreatedPatientId = useCallback(
    (payload: PatientState["createdPatientId"]) => {
      dispatch({ type: "SET_CREATED_PATIENT", payload });
    },
    [],
  );

  // Insurance
  const setSelectedInsurance = useCallback(
    (payload: PatientState["selectedInsurance"]) => {
      dispatch({ type: "SET_SELECTED_INSURANCE", payload });
    },
    [],
  );

  const handleSetSelectedSinDate = useCallback(
    (payload: PatientState["selectedSinDate"]) => {
      dispatch({ type: "SET_SELECTED_SIN_DATE", payload });
    },
    [],
  );

  // Online
  const handleUpdateOnlineMasterData = useCallback(
    (payload: PatientState["onlineMasterData"]) => {
      dispatch({ type: "UPDATE_ONLINE_MASTER", payload });
    },
    [],
  );

  const handleUpdateOnlineInsuranceData = useCallback(
    (payload: PatientState["onlineInsuranceData"]) => {
      dispatch({ type: "UPDATE_ONLINE_INSURANCE", payload });
    },
    [],
  );

  const handleUpdateOnlineViewResultData = useCallback(
    (payload: PatientState["onlineViewResultData"]) => {
      dispatch({ type: "UPDATE_ONLINE_VIEW_RESULT", payload });
    },
    [],
  );
  const changeConfirmingType = useCallback(
    (payload: PatientState["confirmingType"]) => {
      dispatch({ type: "CHANGE_CONFIRMING_TYPE", payload });
    },
    [],
  );

  const handleSetCallback = useCallback(
    (payload: PatientState["callbackList"]) => {
      dispatch({ type: "SET_CALLBACK", payload });
    },
    [],
  );

  const handleUpdateCallback = useCallback(
    (payload: PatientState["callbackList"]) => {
      dispatch({ type: "UPDATE_CALLBACK", payload });
    },
    [],
  );

  const handleSetOnlineConfirmationHistoryData = useCallback(
    (payload: PatientState["onlineConfirmHistoryData"]) => {
      dispatch({ type: "SET_ONLINE_HISTORY", payload });
    },
    [],
  );

  const handleSetListHokenCheck = useCallback(
    (payload: PatientState["listHokenCheck"]) => {
      dispatch({ type: "SET_LIST_HOKEN_CHECK", payload });
    },
    [],
  );

  const handleSetSystemNotifications = useCallback(
    (payload: PatientState["systemNotifications"]) => {
      dispatch({ type: "SET_SYSTEM_NOTIFICATIONS", payload });
    },
    [],
  );

  const handleSetReadSystemNotifications = useCallback(
    (payload: PatientState["readSystemNotifications"]) => {
      dispatch({ type: "SET_READ_SYSTEM_NOTIFICATIONS", payload });
    },
    [],
  );

  const handleSetRefetchReadSystemNotifications = useCallback(
    (payload: PatientState["refetchReadSystemNotifications"]) => {
      dispatch({ type: "SET_REFETCH_READ_SYSTEM_NOTIFICATIONS", payload });
    },
    [],
  );

  const resetOnlineData = useCallback(() => {
    changeConfirmingType(null);
    dispatch({ type: "RESET_ONLINE_DATA" });
  }, [changeConfirmingType]);

  const resetAllData = useCallback(() => {
    handleSetCallback([]);
    changeConfirmingType(null);
    dispatch({ type: "RESET_ONLINE_DATA" });
    if (
      state.confirmingType &&
      [
        "CONFIRMING_HOKEN_MY_INSURANCE",
        "CONFIRMING_KOHI_MY_INSURANCE",
        "ADDING_HOKEN_MY_INSURANCE",
        "EDITING_HOKEN_MY_INSURANCE",
        "EDITING_KOHI_MY_INSURANCE",
        "ADDING_KOHI_MY_INSURANCE",
      ].includes(state.confirmingType)
    ) {
      return;
    }
    handleCloseAllModal();

    if (
      !state.modal.newPatientOpen &&
      state.confirmingType !== "ADDING_RECEPTION_MY_CARD"
    ) {
      handleSetPatientProps(null);
      handleSetPatient(null);
    }
  }, [
    changeConfirmingType,
    handleCloseAllModal,
    handleSetCallback,
    handleSetPatient,
    handleSetPatientProps,
    state.confirmingType,
    state.modal.newPatientOpen,
  ]);

  // Effect
  useEffect(() => {
    if (!state.confirmingType) return;
    if (
      [
        "ADDING_PATIENT_MY_CARD",
        "ADDING_PATIENT_MY_INSURANCE",
        "VIEW_RESULT_MY_CARD_ADD_PATIENT",
      ].includes(state.confirmingType)
    ) {
      if (!state.callbackList[0]) return;
      state.callbackList[0]();
    }
  }, [state.callbackList, state.confirmingType]);

  // state
  const value = useMemo(
    (): PatientContextType => ({
      ...state,

      handleOpenModal,
      handleCloseModal,
      handleCloseAllModal,
      handleOpenDuplicateInsuranceCardModal,
      handleOpenDuplicatePublicExpenseModal,
      handleCloseDuplicateInsuranceCardModal,
      handleCloseDuplicatePublicExpenseModal,

      handleSetPatient,
      handleSetPatientProps,
      handleSetPatientReservationProps,

      setSelectedInsurance,
      handleSetSelectedSinDate,

      changeConfirmingType,
      handleUpdateOnlineMasterData,
      handleUpdateOnlineInsuranceData,
      resetAllData,
      handleSetCallback,
      handleUpdateCallback,
      resetOnlineData,
      handleSetOnlineConfirmationHistoryData,
      handleSetListHokenCheck,
      handleUpdateOnlineViewResultData,
      handleSetSystemNotifications,
      handleSetReadSystemNotifications,
      handleSetRefetchReadSystemNotifications,
      handleSetCreatedPatientId,
    }),
    [
      state,
      handleOpenModal,
      handleCloseModal,
      handleCloseAllModal,
      handleOpenDuplicateInsuranceCardModal,
      handleOpenDuplicatePublicExpenseModal,
      handleCloseDuplicateInsuranceCardModal,
      handleCloseDuplicatePublicExpenseModal,
      handleSetPatient,
      handleSetPatientProps,
      handleSetPatientReservationProps,
      setSelectedInsurance,
      handleSetSelectedSinDate,
      changeConfirmingType,
      handleUpdateOnlineMasterData,
      handleUpdateOnlineInsuranceData,
      resetAllData,
      handleSetCallback,
      handleUpdateCallback,
      resetOnlineData,
      handleSetOnlineConfirmationHistoryData,
      handleSetListHokenCheck,
      handleUpdateOnlineViewResultData,
      handleSetSystemNotifications,
      handleSetReadSystemNotifications,
      handleSetRefetchReadSystemNotifications,
      handleSetCreatedPatientId,
    ],
  );

  return (
    <PatientContext.Provider value={value}>
      {children}
      <RenderIf
        condition={
          !!state.selectedPatient && state.modal.reservationHistoryOpen
        }
      >
        <PatientReservationHistoryModal
          isOpen
          onClose={() => {
            handleCloseModal("RESERVATION_HISTORY");
            handleSetPatient(null);
          }}
          showReservationModal={() => handleOpenModal("RESERVATION")}
          patient={state.selectedPatient!}
        />
      </RenderIf>
      <RenderIf
        condition={!!state.selectedPatient && state.modal.reservationOpen}
      >
        <PatientReservationModal
          isOpen
          onClose={() => {
            handleCloseModal("RESERVATION");
            handleSetPatientReservationProps(undefined);
          }}
          patient={state.selectedPatient!}
        />
      </RenderIf>

      <RenderIf condition={state.modal.newPatientOpen}>
        <NewPatientModal />
      </RenderIf>

      <RenderIf condition={state.modal.insuranceSelectionOpen}>
        <InsuranceSelectionModal />
      </RenderIf>

      <RenderIf condition={state.modal.handleWorkRelatedInjuryOpen}>
        <WorkRelatedInjuryModal />
      </RenderIf>

      <RenderIf condition={state.modal.handleInsuranceCardOpen}>
        <InsuranceCardModal />
      </RenderIf>

      <RenderIf condition={state.modal.handleAutomobileInsuranceOpen}>
        <AutomobileInsuranceModal />
      </RenderIf>

      <RenderIf condition={state.modal.handlePublicExpense}>
        <HandleKohiModal />
      </RenderIf>

      <RenderIf
        condition={
          !!state.confirmingType &&
          ["ADDING_RECEPTION_MY_CARD", "VIEW_RESULT_MY_CARD"].includes(
            state.confirmingType,
          )
        }
      >
        <HandleAddReceptionMyCard />
      </RenderIf>

      <RenderIf
        condition={
          !!state.confirmingType &&
          [
            "CONFIRMING_HOKEN_MY_INSURANCE",
            "CONFIRMING_KOHI_MY_INSURANCE",
            "ADDING_HOKEN_MY_INSURANCE",
            "EDITING_HOKEN_MY_INSURANCE",
            "EDITING_KOHI_MY_INSURANCE",
            "ADDING_KOHI_MY_INSURANCE",
          ].includes(state.confirmingType)
        }
      >
        <HandleMyInsuranceCard />
      </RenderIf>

      <RenderIf condition={state.modal.onlineQualificationVerificationOpen}>
        <OnlineQualificationVertificationModal />
      </RenderIf>
      <RenderIf condition={state.modal.qualificationConfirmationHistoryOpen}>
        <QualificationConfirmationHistoryModal />
      </RenderIf>
    </PatientContext.Provider>
  );
};

export const usePatientContext = (): PatientContextType => {
  const context = useContext(PatientContext);
  if (!context) {
    throw new Error(
      "usePatientContextContext must be used within an PatientContextProvider",
    );
  }
  return context;
};
