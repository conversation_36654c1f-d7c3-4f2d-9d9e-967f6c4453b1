{"name": "denkaru-user-web", "version": "0.0.1", "license": "UNLICENSED", "scripts": {"dev": "cross-env NEXT_PUBLIC_ENV=local-dev NODE_OPTIONS='--trace-deprecation --max_old_space_size=8192' next dev", "dev:static": "yarn build:local-static && serve out -l 3000", "build:local-static": "rm -rf .next out && cross-env NEXT_PUBLIC_ENV=local-dev NEXT_PUBLIC_STATIC_EXPORT=true NODE_OPTIONS='--trace-deprecation --max_old_space_size=8192' next build", "build:dev": "rm -rf .next out && NEXT_PUBLIC_ENV=dev NEXT_PUBLIC_STATIC_EXPORT=true NODE_OPTIONS='--trace-deprecation --max_old_space_size=8192' next build", "build:stg": "rm -rf .next out && NEXT_PUBLIC_ENV=stg NEXT_PUBLIC_STATIC_EXPORT=true NODE_OPTIONS='--max_old_space_size=8192' next build", "build:prd": "rm -rf .next out && NEXT_PUBLIC_ENV=prd NEXT_PUBLIC_STATIC_EXPORT=true NODE_OPTIONS='--max_old_space_size=8192' next build", "build:analyze": "rm -rf .next out && cross-env NEXT_PUBLIC_ENV=prd NEXT_PUBLIC_STATIC_EXPORT=true NODE_OPTIONS='--max_old_space_size=8192' BUNDLE_ANALYZE=true next build", "lint": "NODE_OPTIONS='--max_old_space_size=8192' next lint --quiet", "lint:fix": "NODE_OPTIONS='--max_old_space_size=8192' next lint --fix --quiet", "lint:config": "npx eslint --inspect-config", "type-check": "NODE_OPTIONS='--max_old_space_size=8192' tsc --noEmit", "dep-check": "knip --dependencies", "file-check": "knip --files", "export-check": "knip --exports", "format": "prettier --write --ignore-unknown", "icon": "npx @svgr/cli -d src/components/ui/Icon --no-index ${0}", "codegen": "dotenvx run --env-file=.env.local-dev -- graphql-codegen --config graphql-codegen.ts", "denkaru-codegen": "ts-node denkaru-codegen.ts && prettier --write src/constants/denkaru-codes.ts", "env": "cp .env.local-dev.example .env.local-dev"}, "dependencies": {"@apollo/client": "^3.10.8", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@holiday-jp/holiday_jp": "^2.4.0", "@hookform/resolvers": "^3.9.1", "@microsoft/signalr": "^8.0.7", "@next/third-parties": "^15.1.4", "@sentry/nextjs": "^8.51.0", "@szhsin/react-menu": "^4.2.3", "@react-pdf/renderer": "^4.3.0", "@tanstack/react-virtual": "^3.8.6", "amazon-chime-sdk-component-library-react": "^3.11.0", "amazon-chime-sdk-js": "^3.27.1", "antd": "^5.20.0", "autolinker": "^4.0.0", "axios": "^1.7.4", "chart.js": "^4.4.3", "chartjs-plugin-zoom": "^2.0.1", "dayjs": "^1.11.12", "dexie": "^4.0.8", "file-saver": "^2.0.5", "google-libphonenumber": "^3.2.38", "graphql": "^16.9.0", "graphql-ws": "^5.16.0", "hashids": "^2.3.0", "html-react-parser": "^5.2.2", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "loglevel": "^1.9.2", "next": "15.1.4", "pdfjs-dist": "^5.0.375", "qs": "^6.14.0", "qrcode": "^1.5.4", "quill-magic-url": "^4.2.0", "react": "^18.2.0", "react-big-calendar": "^1.18.0", "react-dom": "^18.2.0", "react-highlight-words": "^0.21.0", "react-hook-form": "^7.53.0", "react-intersection-observer": "^9.13.1", "react-quill-new": "^3.3.2", "react-resizable": "^3.0.5", "react-responsive": "^10.0.1", "react-speech-recognition": "^3.10.0", "react-virtuoso": "^4.9.0", "recharts": "^2.14.1", "styled-system": "^5.1.5", "survey-creator-react": "^1.10.2", "survey-react": "^1.12.4", "three": "0.164.1", "uuid": "^10.0.0", "wanakana": "^5.3.1", "yakuhanjp": "^4.1.1", "yup": "^1.5.0"}, "devDependencies": {"@dotenvx/dotenvx": "^1.21.0", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@graphql-codegen/cli": "^5.0.0", "@graphql-codegen/introspection": "^4.0.3", "@graphql-codegen/near-operation-file-preset": "^3.0.0", "@graphql-codegen/typescript": "^4.1.0", "@graphql-codegen/typescript-operations": "^4.2.3", "@graphql-codegen/typescript-react-apollo": "^4.3.0", "@next/bundle-analyzer": "^15.1.5", "@ricky0123/vad-react": "^0.0.24", "@ricky0123/vad-web": "^0.0.18", "@svgr/cli": "^8.1.0", "@types/file-saver": "^2.0.7", "@types/google-libphonenumber": "^7.4.30", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.7", "@types/qs": "^6.9.18", "@types/qrcode": "^1.5.5", "@types/react": "^18.2.38", "@types/react-big-calendar": "^1.16.2", "@types/react-dom": "18.2.25", "@types/react-highlight-words": "^0.20.0", "@types/react-resizable": "^3.0.7", "@types/styled-components": "^5.1.31", "@types/three": "0.164.1", "@types/uuid": "^10.0.0", "@welldone-software/why-did-you-render": "^8", "copy-webpack-plugin": "^12.0.2", "cross-env": "^7.0.3", "eslint": "^9.12.0", "eslint-config-next": "^15.1.4", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-unused-imports": "^4.1.4", "knip": "^5.41.1", "lefthook": "^1.10.10", "prettier": "^3.4.2", "serve": "^14.2.3", "styled-components": "^6.1.16", "ts-node": "^10.9.2", "typescript": "5.7.2", "typescript-eslint": "^8.20.0"}, "volta": {"node": "22.16.0", "yarn": "1.22.22"}, "packageManager": "yarn@1.22.22+sha1.ac34549e6aa8e7ead463a7407e1c7390f61a6610"}